// Import the class directly to test methods
class TestImageCacheService {
  constructor() {
    this.dbName = 'BookTokImageCache';
    this.dbVersion = 1;
    this.storeName = 'images';
    this.maxCacheSize = 50 * 1024 * 1024;
    this.maxAge = 30 * 24 * 60 * 60 * 1000;
    this.db = null;
  }
}

// Mock IndexedDB
const mockDB = {
  transaction: jest.fn(),
  objectStoreNames: { contains: jest.fn() }
};

const mockObjectStore = {
  get: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  clear: jest.fn(),
  getAll: jest.fn(),
  index: jest.fn()
};

const mockTransaction = {
  objectStore: jest.fn(() => mockObjectStore),
  oncomplete: null,
  onerror: null
};

const mockIndex = {
  getAll: jest.fn()
};

// Mock global objects
global.indexedDB = {
  open: jest.fn()
};

global.URL = {
  createObjectURL: jest.fn((blob) => `blob:mock-url-${Date.now()}`),
  revokeObjectURL: jest.fn()
};

global.fetch = jest.fn();

describe('ImageCacheService', () => {
  let service;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    
    // Create new instance to reset internal state  
    service = new TestImageCacheService();
    service.db = null;

    // Setup default mock behaviors
    mockDB.transaction.mockReturnValue(mockTransaction);
    mockObjectStore.index.mockReturnValue(mockIndex);
  });

  describe('initDB', () => {
    it('should open IndexedDB and cache the connection', async () => {
      const mockRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: mockDB
      };

      global.indexedDB.open.mockReturnValue(mockRequest);

      const dbPromise = service.initDB();
      
      // Simulate success
      mockRequest.onsuccess();

      const db = await dbPromise;
      expect(db).toBe(mockDB);
      expect(service.db).toBe(mockDB);
      expect(global.indexedDB.open).toHaveBeenCalledWith('BookTokImageCache', 1);
    });

    it('should handle database errors', async () => {
      const mockRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        error: new Error('DB Error')
      };

      global.indexedDB.open.mockReturnValue(mockRequest);

      const dbPromise = service.initDB();
      
      // Simulate error
      mockRequest.onerror();

      await expect(dbPromise).rejects.toThrow('DB Error');
    });

    it('should create object store on upgrade', async () => {
      const mockRequest = {
        onsuccess: null,
        onerror: null,
        onupgradeneeded: null,
        result: mockDB
      };

      const mockUpgradeDB = {
        objectStoreNames: { contains: jest.fn(() => false) },
        createObjectStore: jest.fn()
      };

      const mockStore = {
        createIndex: jest.fn()
      };

      mockUpgradeDB.createObjectStore.mockReturnValue(mockStore);

      global.indexedDB.open.mockReturnValue(mockRequest);

      const dbPromise = service.initDB();
      
      // Simulate upgrade
      const event = { target: { result: mockUpgradeDB } };
      mockRequest.onupgradeneeded(event);
      
      expect(mockUpgradeDB.createObjectStore).toHaveBeenCalledWith('images', { keyPath: 'url' });
      expect(mockStore.createIndex).toHaveBeenCalledWith('timestamp', 'timestamp');
      expect(mockStore.createIndex).toHaveBeenCalledWith('size', 'size');
    });
  });

  describe('getCachedImage', () => {
    beforeEach(() => {
      service.db = mockDB;
    });

    it('should return cached image when valid', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/jpeg' });
      const mockCacheEntry = {
        url: 'https://example.com/image.jpg',
        blob: mockBlob,
        timestamp: Date.now(),
        size: 1000
      };

      const mockRequest = {
        onsuccess: null,
        onerror: null,
        result: mockCacheEntry
      };

      mockObjectStore.get.mockReturnValue(mockRequest);

      const getPromise = service.getCachedImage('https://example.com/image.jpg');
      
      // Simulate success
      mockRequest.onsuccess();

      const result = await getPromise;
      
      expect(result).toBeDefined();
      expect(result.cached).toBe(true);
      expect(result.url).toMatch(/^blob:mock-url-/);
      expect(result.timestamp).toBe(mockCacheEntry.timestamp);
      expect(typeof result.cleanup).toBe('function');
    });

    it('should return null for expired cache entries', async () => {
      const mockCacheEntry = {
        url: 'https://example.com/image.jpg',
        blob: new Blob(['test']),
        timestamp: Date.now() - (31 * 24 * 60 * 60 * 1000), // 31 days old
        size: 1000
      };

      const mockRequest = {
        onsuccess: null,
        onerror: null,
        result: mockCacheEntry
      };

      mockObjectStore.get.mockReturnValue(mockRequest);
      service.removeCachedImage = jest.fn();

      const getPromise = service.getCachedImage('https://example.com/image.jpg');
      mockRequest.onsuccess();

      const result = await getPromise;
      
      expect(result).toBeNull();
      expect(service.removeCachedImage).toHaveBeenCalledWith('https://example.com/image.jpg');
    });

    it('should handle missing cache entries', async () => {
      const mockRequest = {
        onsuccess: null,
        onerror: null,
        result: null
      };

      mockObjectStore.get.mockReturnValue(mockRequest);

      const getPromise = service.getCachedImage('https://example.com/image.jpg');
      mockRequest.onsuccess();

      const result = await getPromise;
      expect(result).toBeNull();
    });

    it('should handle database errors gracefully', async () => {
      service.initDB = jest.fn().mockRejectedValue(new Error('DB Error'));
      service.db = null;

      const result = await service.getCachedImage('https://example.com/image.jpg');
      expect(result).toBeNull();
    });
  });

  describe('cacheImage', () => {
    beforeEach(() => {
      service.db = mockDB;
      service.enforceStorageLimit = jest.fn().mockResolvedValue();
    });

    it('should cache image successfully', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/jpeg' });
      mockBlob.size = 1000;

      const mockRequest = {
        onsuccess: null,
        onerror: null
      };

      mockObjectStore.put.mockReturnValue(mockRequest);

      const cachePromise = service.cacheImage('https://example.com/image.jpg', mockBlob);
      mockRequest.onsuccess();

      const result = await cachePromise;
      
      expect(result).toBe(true);
      expect(service.enforceStorageLimit).toHaveBeenCalled();
      expect(mockObjectStore.put).toHaveBeenCalledWith({
        url: 'https://example.com/image.jpg',
        blob: mockBlob,
        timestamp: expect.any(Number),
        size: 1000
      });
    });

    it('should handle caching errors', async () => {
      const mockBlob = new Blob(['test']);
      const mockRequest = {
        onsuccess: null,
        onerror: null,
        error: new Error('Store Error')
      };

      mockObjectStore.put.mockReturnValue(mockRequest);

      const cachePromise = service.cacheImage('https://example.com/image.jpg', mockBlob);
      mockRequest.onerror();

      const result = await cachePromise;
      expect(result).toBe(false);
    });
  });

  describe('fetchAndCache', () => {
    beforeEach(() => {
      service.cacheImage = jest.fn().mockResolvedValue(true);
      service.notifyServerCached = jest.fn().mockResolvedValue();
    });

    it('should fetch from server temp cache first', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/jpeg' });
      const mockResponse = {
        ok: true,
        blob: jest.fn().mockResolvedValue(mockBlob)
      };

      global.fetch.mockResolvedValue(mockResponse);

      const result = await service.fetchAndCache('https://example.com/image.jpg');

      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/temp-cache/image?url=')
      );
      expect(result.cached).toBe(false);
      expect(result.url).toMatch(/^blob:mock-url-/);
      expect(service.cacheImage).toHaveBeenCalledWith('https://example.com/image.jpg', mockBlob);
      expect(service.notifyServerCached).toHaveBeenCalledWith('https://example.com/image.jpg');
    });

    it('should fallback to direct fetch if temp cache fails', async () => {
      const mockBlob = new Blob(['test'], { type: 'image/jpeg' });
      const mockResponse = {
        ok: true,
        blob: jest.fn().mockResolvedValue(mockBlob)
      };

      global.fetch
        .mockRejectedValueOnce(new Error('Temp cache miss'))
        .mockResolvedValueOnce(mockResponse);

      const result = await service.fetchAndCache('https://example.com/image.jpg');

      expect(global.fetch).toHaveBeenCalledTimes(2);
      expect(global.fetch).toHaveBeenLastCalledWith('https://example.com/image.jpg');
      expect(result.cached).toBe(false);
    });

    it('should handle fetch errors', async () => {
      global.fetch.mockRejectedValue(new Error('Network error'));

      await expect(service.fetchAndCache('https://example.com/image.jpg'))
        .rejects.toThrow('Network error');
    });

    it('should handle non-ok responses', async () => {
      const mockResponse = {
        ok: false,
        status: 404
      };

      global.fetch.mockResolvedValue(mockResponse);

      await expect(service.fetchAndCache('https://example.com/image.jpg'))
        .rejects.toThrow('Failed to fetch image: 404');
    });
  });

  describe('getImage', () => {
    it('should return null for empty URL', async () => {
      const result = await service.getImage('');
      expect(result).toBeNull();
    });

    it('should return cached image if available', async () => {
      const mockCachedResult = {
        url: 'blob:mock-cached',
        cached: true,
        timestamp: Date.now(),
        cleanup: jest.fn()
      };

      service.getCachedImage = jest.fn().mockResolvedValue(mockCachedResult);
      service.fetchAndCache = jest.fn();

      const result = await service.getImage('https://example.com/image.jpg');

      expect(result).toBe(mockCachedResult);
      expect(service.getCachedImage).toHaveBeenCalledWith('https://example.com/image.jpg');
      expect(service.fetchAndCache).not.toHaveBeenCalled();
    });

    it('should fetch and cache if not cached', async () => {
      const mockFetchResult = {
        url: 'blob:mock-fetched',
        cached: false,
        timestamp: Date.now(),
        cleanup: jest.fn()
      };

      service.getCachedImage = jest.fn().mockResolvedValue(null);
      service.fetchAndCache = jest.fn().mockResolvedValue(mockFetchResult);

      const result = await service.getImage('https://example.com/image.jpg');

      expect(result).toBe(mockFetchResult);
      expect(service.getCachedImage).toHaveBeenCalledWith('https://example.com/image.jpg');
      expect(service.fetchAndCache).toHaveBeenCalledWith('https://example.com/image.jpg');
    });
  });

  describe('enforceStorageLimit', () => {
    beforeEach(() => {
      service.db = mockDB;
      service.removeCachedImage = jest.fn().mockResolvedValue(true);
    });

    it('should remove oldest entries when over limit', async () => {
      const mockEntries = [
        { url: 'url1', size: 20 * 1024 * 1024, timestamp: Date.now() - 3000 },
        { url: 'url2', size: 20 * 1024 * 1024, timestamp: Date.now() - 2000 },
        { url: 'url3', size: 20 * 1024 * 1024, timestamp: Date.now() - 1000 }
      ];

      const mockSizeRequest = {
        onsuccess: null,
        onerror: null,
        result: mockEntries
      };

      const mockTimestampRequest = {
        onsuccess: null,
        onerror: null,
        result: mockEntries
      };

      mockIndex.getAll
        .mockReturnValueOnce(mockSizeRequest)
        .mockReturnValueOnce(mockTimestampRequest);

      const enforcePromise = service.enforceStorageLimit();

      // Simulate both requests succeeding
      mockSizeRequest.onsuccess();
      mockTimestampRequest.onsuccess();

      await enforcePromise;

      // Should remove oldest entries to get under 80% of limit (40MB)
      expect(service.removeCachedImage).toHaveBeenCalledWith('url1');
      expect(service.removeCachedImage).toHaveBeenCalledWith('url2');
    });

    it('should not remove entries when under limit', async () => {
      const mockEntries = [
        { url: 'url1', size: 10 * 1024 * 1024, timestamp: Date.now() }
      ];

      const mockSizeRequest = {
        onsuccess: null,
        result: mockEntries
      };

      const mockTimestampRequest = {
        onsuccess: null,
        result: mockEntries
      };

      mockIndex.getAll
        .mockReturnValueOnce(mockSizeRequest)
        .mockReturnValueOnce(mockTimestampRequest);

      const enforcePromise = service.enforceStorageLimit();
      mockSizeRequest.onsuccess();
      mockTimestampRequest.onsuccess();

      await enforcePromise;

      expect(service.removeCachedImage).not.toHaveBeenCalled();
    });
  });

  describe('getCacheStats', () => {
    beforeEach(() => {
      service.db = mockDB;
    });

    it('should return cache statistics', async () => {
      const mockEntries = [
        { url: 'url1', size: 1024 * 1024, timestamp: Date.now() },
        { url: 'url2', size: 2 * 1024 * 1024, timestamp: Date.now() }
      ];

      const mockRequest = {
        onsuccess: null,
        onerror: null,
        result: mockEntries
      };

      mockObjectStore.getAll.mockReturnValue(mockRequest);

      const statsPromise = service.getCacheStats();
      mockRequest.onsuccess();

      const stats = await statsPromise;

      expect(stats).toEqual({
        count: 2,
        totalSize: 3 * 1024 * 1024,
        totalSizeMB: '3.00',
        maxSizeMB: '50.00',
        utilization: '6.0'
      });
    });

    it('should handle errors gracefully', async () => {
      service.initDB = jest.fn().mockRejectedValue(new Error('DB Error'));
      service.db = null;

      const stats = await service.getCacheStats();
      expect(stats).toBeNull();
    });
  });

  describe('preloadImages', () => {
    beforeEach(() => {
      service.getCachedImage = jest.fn();
      service.fetchAndCache = jest.fn();
    });

    it('should preload up to 3 images', async () => {
      const urls = [
        'https://example.com/1.jpg',
        'https://example.com/2.jpg',
        'https://example.com/3.jpg',
        'https://example.com/4.jpg'
      ];

      service.getCachedImage.mockResolvedValue(null);

      await service.preloadImages(urls);

      // Should only check first 3
      expect(service.getCachedImage).toHaveBeenCalledTimes(3);
      expect(service.fetchAndCache).toHaveBeenCalledTimes(3);
    });

    it('should skip already cached images', async () => {
      const urls = ['https://example.com/1.jpg', 'https://example.com/2.jpg'];

      service.getCachedImage
        .mockResolvedValueOnce({ url: 'blob:cached', cached: true })
        .mockResolvedValueOnce(null);

      await service.preloadImages(urls);

      expect(service.getCachedImage).toHaveBeenCalledTimes(2);
      expect(service.fetchAndCache).toHaveBeenCalledTimes(1);
      expect(service.fetchAndCache).toHaveBeenCalledWith('https://example.com/2.jpg');
    });

    it('should filter out null/undefined URLs', async () => {
      const urls = ['https://example.com/1.jpg', null, undefined, '', 'https://example.com/2.jpg'];

      await service.preloadImages(urls);

      expect(service.getCachedImage).toHaveBeenCalledTimes(2);
    });
  });

  describe('clearCache', () => {
    beforeEach(() => {
      service.db = mockDB;
    });

    it('should clear all cached images', async () => {
      const mockRequest = {
        onsuccess: null,
        onerror: null
      };

      mockObjectStore.clear.mockReturnValue(mockRequest);

      const clearPromise = service.clearCache();
      mockRequest.onsuccess();

      const result = await clearPromise;

      expect(result).toBe(true);
      expect(mockObjectStore.clear).toHaveBeenCalled();
    });

    it('should handle clear errors', async () => {
      const mockRequest = {
        onsuccess: null,
        onerror: null,
        error: new Error('Clear Error')
      };

      mockObjectStore.clear.mockReturnValue(mockRequest);

      const clearPromise = service.clearCache();
      mockRequest.onerror();

      const result = await clearPromise;
      expect(result).toBe(false);
    });
  });
});