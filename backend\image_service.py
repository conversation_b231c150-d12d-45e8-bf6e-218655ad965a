"""
Image download and caching service for book covers
"""
import os
import httpx
import asyncio
from pathlib import Path
from typing import Optional
from PIL import Image
from io import BytesIO
import hashlib

class ImageService:
    def __init__(self, covers_dir: str = "static/covers"):
        self.covers_dir = Path(covers_dir)
        self.covers_dir.mkdir(parents=True, exist_ok=True)
        
    def get_cover_filename(self, book_id: int, isbn: Optional[str] = None) -> str:
        """Generate a filename for the book cover"""
        if isbn:
            return f"{isbn}.jpg"
        return f"book_{book_id}.jpg"
    
    def get_local_path(self, book_id: int, isbn: Optional[str] = None) -> str:
        """Get the local file path for a book cover"""
        filename = self.get_cover_filename(book_id, isbn)
        return str(self.covers_dir / filename)
    
    def get_url_path(self, book_id: int, isbn: Optional[str] = None) -> str:
        """Get the URL path for serving the cover"""
        filename = self.get_cover_filename(book_id, isbn)
        return f"/static/covers/{filename}"
    
    def cover_exists(self, book_id: int, isbn: Optional[str] = None) -> bool:
        """Check if a cover already exists locally"""
        return Path(self.get_local_path(book_id, isbn)).exists()
    
    async def download_and_optimize_cover(
        self, 
        cover_url: str, 
        book_id: int, 
        isbn: Optional[str] = None,
        max_width: int = 400,
        quality: int = 85
    ) -> Optional[str]:
        """
        Download a book cover from URL and optimize it
        Returns the local URL path if successful, None otherwise
        """
        try:
            # Skip if already exists
            if self.cover_exists(book_id, isbn):
                return self.get_url_path(book_id, isbn)
            
            # Download the image
            print(f"Downloading cover from: {cover_url}")
            async with httpx.AsyncClient() as client:
                response = await client.get(cover_url, follow_redirects=True, timeout=30.0)
                response.raise_for_status()
                print(f"Successfully downloaded {len(response.content)} bytes")
                
            # Open and optimize the image
            img = Image.open(BytesIO(response.content))
            
            # Convert to RGB if necessary (for PNG with transparency)
            if img.mode in ('RGBA', 'LA', 'P'):
                rgb_img = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                rgb_img.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
                img = rgb_img
            
            # Resize if too large
            if img.width > max_width:
                ratio = max_width / img.width
                new_height = int(img.height * ratio)
                img = img.resize((max_width, new_height), Image.Resampling.LANCZOS)
            
            # Save optimized image
            local_path = self.get_local_path(book_id, isbn)
            img.save(local_path, 'JPEG', quality=quality, optimize=True)
            
            return self.get_url_path(book_id, isbn)
            
        except Exception as e:
            print(f"Error downloading cover for book {book_id}: {str(e)}")
            return None
    
    async def download_multiple_covers(self, books_data: list) -> dict:
        """
        Download multiple book covers concurrently
        books_data: list of dicts with 'id', 'cover_url', and optionally 'isbn'
        Returns: dict mapping book_id to local URL path
        """
        tasks = []
        book_ids = []
        
        for book in books_data:
            # Handle both integer and string IDs
            book_id = book['id']
            if isinstance(book_id, str) and book_id.startswith('suggestion_'):
                # For migration script, use the suggestion ID
                book_id_for_file = int(book_id.replace('suggestion_', ''))
            else:
                # For regular usage
                book_id_for_file = book_id
                
            task = self.download_and_optimize_cover(
                book['cover_url'],
                book_id_for_file,
                book.get('isbn')
            )
            tasks.append(task)
            book_ids.append(book['id'])  # Keep original ID for mapping
        
        results = await asyncio.gather(*tasks)
        
        return {
            book_id: result 
            for book_id, result in zip(book_ids, results) 
            if result is not None
        }

# Global instance
image_service = ImageService()