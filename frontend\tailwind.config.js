/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        watercolor: {
          yellow: '#ffeaa7',
          orange: '#fab1a0',
          blue: '#74b9ff',
          purple: '#a29bfe',
          pink: '#fd79a8',
        }
      },
      animation: {
        'watercolor-flow': 'watercolorFlow 20s ease infinite',
        'float': 'float 15s ease-in-out infinite',
        'pulse-gentle': 'pulseGentle 2s ease-in-out infinite',
        'watercolor-pulse': 'watercolorPulse 8s ease-in-out infinite',
        'border-flow': 'borderFlow 5s ease-in-out infinite',
      },
      keyframes: {
        watercolorFlow: {
          '0%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' },
          '100%': { backgroundPosition: '0% 50%' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0) scale(1)' },
          '50%': { transform: 'translateY(-20px) scale(1.1)' },
        },
        pulseGentle: {
          '0%, 100%': { opacity: '0.5' },
          '50%': { opacity: '1' },
        },
        watercolorPulse: {
          '0%, 100%': { transform: 'rotate(0deg) scale(1)' },
          '50%': { transform: 'rotate(180deg) scale(1.1)' },
        },
        borderFlow: {
          '0%, 100%': { opacity: '0.5', transform: 'scale(1)' },
          '50%': { opacity: '0.8', transform: 'scale(1.05)' },
        },
      },
      fontFamily: {
        'serif': ['Georgia', 'serif'],
      },
      backgroundImage: {
        'watercolor-gradient': 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 25%, #74b9ff 50%, #a29bfe 75%, #fd79a8 100%)',
      },
      backdropBlur: {
        'xs': '2px',
      },
      perspective: {
        '1000': '1000px',
      }
    },
  },
  plugins: [],
}