import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import BookCoverImage from '../BookCoverImage';
import useImageCache from '../../hooks/useImageCache';

// Mock the useImageCache hook
jest.mock('../../hooks/useImageCache');

describe('BookCoverImage', () => {
  const defaultMockReturn = {
    src: 'https://example.com/cached-image.jpg',
    isLoading: false,
    error: null,
    isCached: true,
    isRefreshing: false
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useImageCache.mockReturnValue(defaultMockReturn);
  });

  describe('basic rendering', () => {
    it('should render image with cached source', () => {
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book" 
        />
      );

      const img = screen.getByRole('img');
      expect(img).toHaveAttribute('src', 'https://example.com/cached-image.jpg');
      expect(img).toHaveAttribute('alt', 'Test Book');
    });

    it('should apply custom className', () => {
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          className="custom-class" 
        />
      );

      const img = screen.getByRole('img');
      expect(img).toHaveClass('custom-class');
    });

    it('should pass preloadUrls to hook', () => {
      const preloadUrls = ['https://example.com/next1.jpg', 'https://example.com/next2.jpg'];
      
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          preloadUrls={preloadUrls}
        />
      );

      expect(useImageCache).toHaveBeenCalledWith(
        'https://example.com/book.jpg',
        expect.objectContaining({
          preloadUrls
        })
      );
    });
  });

  describe('loading states', () => {
    it('should show loading indicator when loading', () => {
      useImageCache.mockReturnValue({
        ...defaultMockReturn,
        isLoading: true,
        src: null
      });

      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book" 
        />
      );

      expect(screen.getByRole('img')).toHaveClass('opacity-0');
      // Check for loading spinner
      const loadingDiv = screen.getByRole('img').parentElement.querySelector('.animate-spin');
      expect(loadingDiv).toBeInTheDocument();
    });

    it('should show image with full opacity when loaded', () => {
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book" 
        />
      );

      expect(screen.getByRole('img')).toHaveClass('opacity-100');
    });
  });

  describe('cache indicators', () => {
    it('should show cache indicator when enabled and cached', () => {
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          showCacheIndicator={true}
        />
      );

      expect(screen.getByTitle('Cached locally')).toBeInTheDocument();
      expect(screen.getByText('⚡')).toBeInTheDocument();
    });

    it('should show refresh indicator when refreshing', () => {
      useImageCache.mockReturnValue({
        ...defaultMockReturn,
        isRefreshing: true
      });

      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          showCacheIndicator={true}
        />
      );

      expect(screen.getByTitle('Updating cache')).toBeInTheDocument();
      expect(screen.getByText('🔄')).toBeInTheDocument();
    });

    it('should not show indicators when disabled', () => {
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          showCacheIndicator={false}
        />
      );

      expect(screen.queryByTitle('Cached locally')).not.toBeInTheDocument();
    });

    it('should not show indicators when loading', () => {
      useImageCache.mockReturnValue({
        ...defaultMockReturn,
        isLoading: true
      });

      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          showCacheIndicator={true}
        />
      );

      expect(screen.queryByTitle('Cached locally')).not.toBeInTheDocument();
    });
  });

  describe('error handling', () => {
    it('should show error message on cache error', () => {
      useImageCache.mockReturnValue({
        ...defaultMockReturn,
        src: 'https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover',
        error: 'Failed to load image'
      });

      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book" 
        />
      );

      expect(screen.getByText('Failed to load cover image')).toBeInTheDocument();
    });

    it('should call onError callback', () => {
      const onError = jest.fn();
      
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          onError={onError}
        />
      );

      expect(useImageCache).toHaveBeenCalledWith(
        'https://example.com/book.jpg',
        expect.objectContaining({
          onError
        })
      );
    });

    it('should call onLoad callback', () => {
      const onLoad = jest.fn();
      
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          onLoad={onLoad}
        />
      );

      expect(useImageCache).toHaveBeenCalledWith(
        'https://example.com/book.jpg',
        expect.objectContaining({
          onLoad
        })
      );
    });
  });

  describe('non-cached mode', () => {
    it('should not use cache when disabled', () => {
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          enableCache={false}
        />
      );

      expect(useImageCache).toHaveBeenCalledWith(null, expect.any(Object));
      const img = screen.getByRole('img');
      expect(img).toHaveAttribute('src', 'https://example.com/book.jpg');
    });

    it('should handle image error in non-cached mode', () => {
      const onError = jest.fn();
      
      render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          enableCache={false}
          onError={onError}
        />
      );

      const img = screen.getByRole('img');
      fireEvent.error(img);

      expect(onError).toHaveBeenCalledWith({
        url: 'https://example.com/book.jpg',
        error: 'Image load failed'
      });
    });

    it('should show placeholder on error in non-cached mode', () => {
      const { rerender } = render(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          enableCache={false}
        />
      );

      const img = screen.getByRole('img');
      fireEvent.error(img);

      // Component should re-render with placeholder
      rerender(
        <BookCoverImage 
          src="https://example.com/book.jpg" 
          alt="Test Book"
          enableCache={false}
        />
      );

      expect(screen.getByRole('img')).toHaveAttribute(
        'src', 
        'https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover'
      );
    });

    it('should reset fallback error when src changes', () => {
      const { rerender } = render(
        <BookCoverImage 
          src="https://example.com/book1.jpg" 
          alt="Test Book"
          enableCache={false}
        />
      );

      const img = screen.getByRole('img');
      fireEvent.error(img);

      rerender(
        <BookCoverImage 
          src="https://example.com/book2.jpg" 
          alt="Test Book"
          enableCache={false}
        />
      );

      expect(screen.getByRole('img')).toHaveAttribute('src', 'https://example.com/book2.jpg');
    });
  });

  describe('placeholder behavior', () => {
    it('should use placeholder when no src provided', () => {
      render(
        <BookCoverImage 
          src={null} 
          alt="Test Book"
          enableCache={false}
        />
      );

      expect(screen.getByRole('img')).toHaveAttribute(
        'src',
        'https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover'
      );
    });

    it('should use placeholder when empty src provided', () => {
      render(
        <BookCoverImage 
          src="" 
          alt="Test Book"
          enableCache={false}
        />
      );

      expect(screen.getByRole('img')).toHaveAttribute(
        'src',
        'https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover'
      );
    });
  });

  describe('integration scenarios', () => {
    it('should transition from loading to loaded state', async () => {
      const { rerender } = render(<BookCoverImage src="https://example.com/book.jpg" alt="Test Book" />);

      // Initially loading
      useImageCache.mockReturnValue({
        ...defaultMockReturn,
        isLoading: true,
        src: null
      });
      
      rerender(<BookCoverImage src="https://example.com/book.jpg" alt="Test Book" />);
      expect(screen.getByRole('img')).toHaveClass('opacity-0');

      // Then loaded
      useImageCache.mockReturnValue(defaultMockReturn);
      
      rerender(<BookCoverImage src="https://example.com/book.jpg" alt="Test Book" />);
      expect(screen.getByRole('img')).toHaveClass('opacity-100');
    });

    it('should handle rapid src changes', () => {
      const { rerender } = render(
        <BookCoverImage src="https://example.com/book1.jpg" alt="Book 1" />
      );

      // Quick succession of changes
      rerender(<BookCoverImage src="https://example.com/book2.jpg" alt="Book 2" />);
      rerender(<BookCoverImage src="https://example.com/book3.jpg" alt="Book 3" />);

      expect(useImageCache).toHaveBeenLastCalledWith(
        'https://example.com/book3.jpg',
        expect.any(Object)
      );
    });
  });

  describe('accessibility', () => {
    it('should maintain alt text in all states', () => {
      // Loading state
      useImageCache.mockReturnValue({
        ...defaultMockReturn,
        isLoading: true,
        src: null
      });

      const { rerender } = render(
        <BookCoverImage src="https://example.com/book.jpg" alt="Important Book Cover" />
      );

      expect(screen.getByRole('img')).toHaveAttribute('alt', 'Important Book Cover');

      // Error state
      useImageCache.mockReturnValue({
        ...defaultMockReturn,
        src: 'https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover',
        error: 'Failed to load'
      });

      rerender(
        <BookCoverImage src="https://example.com/book.jpg" alt="Important Book Cover" />
      );

      expect(screen.getByRole('img')).toHaveAttribute('alt', 'Important Book Cover');
    });

    it('should have proper ARIA attributes for loading state', () => {
      useImageCache.mockReturnValue({
        ...defaultMockReturn,
        isLoading: true
      });

      render(
        <BookCoverImage src="https://example.com/book.jpg" alt="Test Book" />
      );

      const container = screen.getByRole('img').parentElement;
      expect(container).toBeInTheDocument();
    });
  });
});