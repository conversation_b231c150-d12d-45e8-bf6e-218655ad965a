class ImageCacheService {
  constructor() {
    this.dbName = 'BookTokImageCache';
    this.dbVersion = 1;
    this.storeName = 'images';
    this.maxCacheSize = 50 * 1024 * 1024; // 50MB
    this.maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
    this.db = null;
  }

  async initDB() {
    if (this.db) return this.db;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'url' });
          store.createIndex('timestamp', 'timestamp');
          store.createIndex('size', 'size');
        }
      };
    });
  }

  async getCachedImage(url) {
    try {
      await this.initDB();
      
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.get(url);
        request.onsuccess = () => {
          const result = request.result;
          
          if (result && this.isValidCache(result)) {
            // Return blob URL for immediate use
            const blobUrl = URL.createObjectURL(result.blob);
            resolve({
              url: blobUrl,
              cached: true,
              timestamp: result.timestamp,
              cleanup: () => URL.revokeObjectURL(blobUrl)
            });
          } else {
            // Clean up expired cache entry
            if (result) {
              this.removeCachedImage(url);
            }
            resolve(null);
          }
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Error getting cached image:', error);
      return null;
    }
  }

  async cacheImage(url, blob) {
    try {
      await this.initDB();
      
      // Check cache size before adding
      await this.enforceStorageLimit();
      
      const cacheEntry = {
        url,
        blob,
        timestamp: Date.now(),
        size: blob.size
      };

      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.put(cacheEntry);
        request.onsuccess = () => resolve(true);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Error caching image:', error);
      return false;
    }
  }

  async fetchAndCache(url) {
    try {
      // Try server temp cache first
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
      const tempCacheUrl = `${apiUrl}/api/temp-cache/image?url=${encodeURIComponent(url)}`;
      
      let response;
      let fromTempCache = false;
      
      try {
        response = await fetch(tempCacheUrl);
        
        if (response.ok) {
          fromTempCache = true;
        } else if (response.status === 404) {
          // Image not found in temp cache, this is expected
          console.debug('Image not in temp cache, fetching directly:', url);
          response = await fetch(url);
        } else {
          // Other error from temp cache, log but try direct fetch
          console.warn(`Temp cache error (${response.status}), falling back to direct fetch:`, url);
          response = await fetch(url);
        }
      } catch (tempCacheError) {
        // Network error with temp cache, fallback to direct fetch
        console.debug('Temp cache request failed, fetching directly:', tempCacheError.message);
        response = await fetch(url);
      }

      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status}`);
      }

      const blob = await response.blob();
      
      // Cache the image
      await this.cacheImage(url, blob);
      
      // Notify server that client has cached the image
      this.notifyServerCached(url).catch(console.warn);

      const blobUrl = URL.createObjectURL(blob);
      return {
        url: blobUrl,
        cached: false,
        timestamp: Date.now(),
        fromTempCache,
        cleanup: () => URL.revokeObjectURL(blobUrl)
      };
    } catch (error) {
      console.warn('Error fetching and caching image:', error);
      throw error;
    }
  }

  async getImage(url) {
    if (!url) return null;

    // First try cache
    const cached = await this.getCachedImage(url);
    if (cached) {
      return cached;
    }

    // If not cached, fetch and cache
    return await this.fetchAndCache(url);
  }

  async notifyServerCached(url) {
    try {
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:8000';
      await fetch(`${apiUrl}/api/cache/client-cached`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ image_url: url })
      });
    } catch (error) {
      // Non-critical, server cleanup is optimization
      console.debug('Could not notify server of cached image:', error);
    }
  }

  isValidCache(cacheEntry) {
    const age = Date.now() - cacheEntry.timestamp;
    return age < this.maxAge;
  }

  async enforceStorageLimit() {
    try {
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      const sizeRequest = store.index('size').getAll();
      const timestampRequest = store.index('timestamp').getAll();
      
      const [sizeResults, timestampResults] = await Promise.all([
        new Promise((resolve, reject) => {
          sizeRequest.onsuccess = () => resolve(sizeRequest.result);
          sizeRequest.onerror = () => reject(sizeRequest.error);
        }),
        new Promise((resolve, reject) => {
          timestampRequest.onsuccess = () => resolve(timestampRequest.result);
          timestampRequest.onerror = () => reject(timestampRequest.error);
        })
      ]);

      const totalSize = sizeResults.reduce((sum, entry) => sum + entry.size, 0);
      
      if (totalSize > this.maxCacheSize) {
        // Remove oldest entries until under limit
        const sortedByTimestamp = timestampResults.sort((a, b) => a.timestamp - b.timestamp);
        let removedSize = 0;
        const targetRemoval = totalSize - (this.maxCacheSize * 0.8); // Remove to 80% capacity
        
        for (const entry of sortedByTimestamp) {
          if (removedSize >= targetRemoval) break;
          await this.removeCachedImage(entry.url);
          removedSize += entry.size;
        }
      }
    } catch (error) {
      console.warn('Error enforcing storage limit:', error);
    }
  }

  async removeCachedImage(url) {
    try {
      await this.initDB();
      
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.delete(url);
        request.onsuccess = () => resolve(true);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Error removing cached image:', error);
      return false;
    }
  }

  async clearCache() {
    try {
      await this.initDB();
      
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.clear();
        request.onsuccess = () => resolve(true);
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Error clearing cache:', error);
      return false;
    }
  }

  async getCacheStats() {
    try {
      await this.initDB();
      
      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.getAll();
        request.onsuccess = () => {
          const entries = request.result;
          const totalSize = entries.reduce((sum, entry) => sum + entry.size, 0);
          const count = entries.length;
          
          resolve({
            count,
            totalSize,
            totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
            maxSizeMB: (this.maxCacheSize / (1024 * 1024)).toFixed(2),
            utilization: ((totalSize / this.maxCacheSize) * 100).toFixed(1)
          });
        };
        request.onerror = () => reject(request.error);
      });
    } catch (error) {
      console.warn('Error getting cache stats:', error);
      return null;
    }
  }

  async preloadImages(urls) {
    const preloadPromises = urls
      .filter(url => url)
      .slice(0, 3) // Limit to 3 for performance
      .map(async (url) => {
        try {
          const cached = await this.getCachedImage(url);
          if (!cached) {
            // Don't await - let it load in background
            this.fetchAndCache(url).catch(console.warn);
          }
        } catch (error) {
          console.warn('Error preloading image:', url, error);
        }
      });

    // Don't await preloading to avoid blocking
    Promise.all(preloadPromises).catch(console.warn);
  }
}

// Export singleton instance
export default new ImageCacheService();