import React from 'react';

const WatercolorBackground = () => {
  return (
    <>
      {/* Main watercolor background */}
      <div className="fixed inset-0 bg-watercolor-gradient bg-[length:400%_400%] animate-watercolor-flow -z-10" />
      
      {/* Watercolor texture overlay */}
      <div className="fixed inset-0 watercolor-texture pointer-events-none z-0" />
      
      {/* Paint splatter effects */}
      <div className="fixed top-[10%] left-[5%] w-36 h-36 paint-splatter rounded-full animate-float" />
      <div 
        className="fixed top-[60%] right-[10%] w-36 h-36 rounded-full animate-float"
        style={{
          background: 'radial-gradient(circle, rgba(116, 185, 255, 0.2) 0%, transparent 70%)',
          filter: 'blur(20px)',
          animationDelay: '5s'
        }}
      />
      <div 
        className="fixed bottom-[20%] left-[15%] w-36 h-36 rounded-full animate-float"
        style={{
          background: 'radial-gradient(circle, rgba(253, 121, 168, 0.2) 0%, transparent 70%)',
          filter: 'blur(20px)',
          animationDelay: '10s'
        }}
      />
      
      {/* Brush stroke decorations */}
      <div className="fixed top-[10%] right-[-50px] w-[300px] h-[100px] opacity-10 pointer-events-none transform -rotate-[15deg]">
        <svg viewBox="0 0 300 100" className="w-full h-full">
          <path 
            d="M10,50 Q50,20 100,50 T200,50 T290,50" 
            stroke="rgba(116, 185, 255, 0.3)" 
            strokeWidth="20" 
            fill="none" 
            strokeLinecap="round"
          />
        </svg>
      </div>
      
      <div className="fixed bottom-[15%] left-[-50px] w-[300px] h-[100px] opacity-10 pointer-events-none transform rotate-[25deg]">
        <svg viewBox="0 0 300 100" className="w-full h-full">
          <path 
            d="M10,50 Q50,80 100,50 T200,50 T290,50" 
            stroke="rgba(253, 121, 168, 0.3)" 
            strokeWidth="20" 
            fill="none" 
            strokeLinecap="round"
          />
        </svg>
      </div>
    </>
  );
};

export default WatercolorBackground;