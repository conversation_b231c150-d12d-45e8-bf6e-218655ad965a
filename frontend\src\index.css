@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    margin: 0;
    font-family: 'Georgia', serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
  }
}

@layer utilities {
  .watercolor-texture {
    background-image: 
      radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
  }
  
  .paint-splatter {
    background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    filter: blur(20px);
  }
  
  .watercolor-border {
    background: linear-gradient(45deg, 
      rgba(116, 185, 255, 0.3) 0%,
      rgba(253, 121, 168, 0.3) 25%,
      rgba(255, 234, 167, 0.3) 50%,
      rgba(162, 155, 254, 0.3) 75%,
      rgba(250, 177, 160, 0.3) 100%);
    filter: blur(10px);
  }
}