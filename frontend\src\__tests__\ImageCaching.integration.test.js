import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import BookCoverImage from '../components/BookCoverImage';
import ImageCacheService from '../services/ImageCacheService';

// Mock fetch for testing
global.fetch = jest.fn();

// Mock IndexedDB
const mockDB = {
  transaction: jest.fn(),
  objectStoreNames: { contains: jest.fn() }
};

const mockObjectStore = {
  get: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  clear: jest.fn(),
  getAll: jest.fn(),
  index: jest.fn()
};

const mockTransaction = {
  objectStore: jest.fn(() => mockObjectStore)
};

global.indexedDB = {
  open: jest.fn()
};

global.URL = {
  createObjectURL: jest.fn(() => 'blob:mock-url'),
  revokeObjectURL: jest.fn()
};

describe('Image Caching Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default IndexedDB mocks
    mockDB.transaction.mockReturnValue(mockTransaction);
    const mockRequest = {
      onsuccess: null,
      onerror: null,
      result: mockDB
    };
    global.indexedDB.open.mockReturnValue(mockRequest);
    
    // Auto-trigger success for DB initialization
    setTimeout(() => {
      if (mockRequest.onsuccess) mockRequest.onsuccess();
    }, 0);
  });

  describe('End-to-End Image Loading Flow', () => {
    it('should complete full caching flow from server temp cache', async () => {
      const imageUrl = 'https://example.com/book-cover.jpg';
      const mockBlob = new Blob(['fake image data'], { type: 'image/jpeg' });

      // Mock successful temp cache response
      global.fetch.mockResolvedValueOnce({
        ok: true,
        blob: jest.fn().mockResolvedValue(mockBlob)
      });

      // Mock IndexedDB operations
      const mockGetRequest = {
        onsuccess: null,
        result: null // Not in cache initially
      };
      const mockPutRequest = {
        onsuccess: null
      };
      
      mockObjectStore.get.mockReturnValue(mockGetRequest);
      mockObjectStore.put.mockReturnValue(mockPutRequest);

      // Render component
      render(<BookCoverImage src={imageUrl} alt="Test Book" showCacheIndicator={true} />);

      // Simulate IndexedDB responses
      setTimeout(() => {
        mockGetRequest.onsuccess();
        mockPutRequest.onsuccess();
      }, 0);

      // Wait for image to load
      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      }, { timeout: 3000 });

      // Verify the flow
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('/api/temp-cache/image?url=')
      );
      expect(mockObjectStore.put).toHaveBeenCalledWith(
        expect.objectContaining({
          url: imageUrl,
          blob: mockBlob
        })
      );
    });

    it('should fallback to direct fetch when temp cache fails', async () => {
      const imageUrl = 'https://example.com/book-cover.jpg';
      const mockBlob = new Blob(['fake image data'], { type: 'image/jpeg' });

      // Mock temp cache failure, then direct fetch success
      global.fetch
        .mockRejectedValueOnce(new Error('Temp cache unavailable'))
        .mockResolvedValueOnce({
          ok: true,
          blob: jest.fn().mockResolvedValue(mockBlob)
        });

      // Mock IndexedDB operations
      const mockGetRequest = { onsuccess: null, result: null };
      const mockPutRequest = { onsuccess: null };
      
      mockObjectStore.get.mockReturnValue(mockGetRequest);
      mockObjectStore.put.mockReturnValue(mockPutRequest);

      render(<BookCoverImage src={imageUrl} alt="Test Book" />);

      setTimeout(() => {
        mockGetRequest.onsuccess();
        mockPutRequest.onsuccess();
      }, 0);

      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      });

      // Should have tried temp cache first, then direct fetch
      expect(global.fetch).toHaveBeenCalledTimes(2);
      expect(global.fetch).toHaveBeenLastCalledWith(imageUrl);
    });

    it('should use cached image on subsequent loads', async () => {
      const imageUrl = 'https://example.com/book-cover.jpg';
      const cachedBlob = new Blob(['cached image'], { type: 'image/jpeg' });

      // Mock cached image in IndexedDB
      const mockGetRequest = {
        onsuccess: null,
        result: {
          url: imageUrl,
          blob: cachedBlob,
          timestamp: Date.now(),
          size: cachedBlob.size
        }
      };
      
      mockObjectStore.get.mockReturnValue(mockGetRequest);

      render(<BookCoverImage src={imageUrl} alt="Test Book" showCacheIndicator={true} />);

      setTimeout(() => {
        mockGetRequest.onsuccess();
      }, 0);

      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      });

      // Should not have made any fetch requests
      expect(global.fetch).not.toHaveBeenCalled();
      
      // Should show cache indicator
      expect(screen.getByTitle('Cached locally')).toBeInTheDocument();
    });
  });

  describe('Error Recovery', () => {
    it('should show placeholder when all image sources fail', async () => {
      const imageUrl = 'https://example.com/nonexistent.jpg';

      // Mock all requests failing
      global.fetch.mockRejectedValue(new Error('Network error'));

      const mockGetRequest = { onsuccess: null, result: null };
      mockObjectStore.get.mockReturnValue(mockGetRequest);

      render(<BookCoverImage src={imageUrl} alt="Test Book" />);

      setTimeout(() => {
        mockGetRequest.onsuccess();
      }, 0);

      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute(
          'src',
          'https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover'
        );
      });

      expect(screen.getByText('Failed to load cover image')).toBeInTheDocument();
    });

    it('should handle IndexedDB failures gracefully', async () => {
      const imageUrl = 'https://example.com/book-cover.jpg';

      // Mock IndexedDB failure
      const mockGetRequest = {
        onerror: null,
        error: new Error('IndexedDB error')
      };
      mockObjectStore.get.mockReturnValue(mockGetRequest);

      // Mock successful direct fetch
      global.fetch.mockResolvedValue({
        ok: true,
        blob: jest.fn().mockResolvedValue(new Blob(['image'], { type: 'image/jpeg' }))
      });

      render(<BookCoverImage src={imageUrl} alt="Test Book" />);

      setTimeout(() => {
        mockGetRequest.onerror();
      }, 0);

      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      });

      // Should still load the image via direct fetch
      expect(global.fetch).toHaveBeenCalled();
    });
  });

  describe('Cache Management Integration', () => {
    it('should notify server when image is successfully cached', async () => {
      const imageUrl = 'https://example.com/book-cover.jpg';
      const mockBlob = new Blob(['image data'], { type: 'image/jpeg' });

      // Mock successful image fetch and caching
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          blob: jest.fn().mockResolvedValue(mockBlob)
        })
        .mockResolvedValueOnce({
          ok: true,
          json: jest.fn().mockResolvedValue({ success: true })
        });

      const mockGetRequest = { onsuccess: null, result: null };
      const mockPutRequest = { onsuccess: null };
      
      mockObjectStore.get.mockReturnValue(mockGetRequest);
      mockObjectStore.put.mockReturnValue(mockPutRequest);

      render(<BookCoverImage src={imageUrl} alt="Test Book" />);

      setTimeout(() => {
        mockGetRequest.onsuccess();
        mockPutRequest.onsuccess();
      }, 0);

      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      });

      // Wait a bit more for notification
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('/api/cache/client-cached'),
          expect.objectContaining({
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ image_url: imageUrl })
          })
        );
      });
    });

    it('should handle server notification failures gracefully', async () => {
      const imageUrl = 'https://example.com/book-cover.jpg';
      const mockBlob = new Blob(['image data'], { type: 'image/jpeg' });

      // Mock successful image fetch but failed notification
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          blob: jest.fn().mockResolvedValue(mockBlob)
        })
        .mockRejectedValueOnce(new Error('Server notification failed'));

      const mockGetRequest = { onsuccess: null, result: null };
      const mockPutRequest = { onsuccess: null };
      
      mockObjectStore.get.mockReturnValue(mockGetRequest);
      mockObjectStore.put.mockReturnValue(mockPutRequest);

      render(<BookCoverImage src={imageUrl} alt="Test Book" />);

      setTimeout(() => {
        mockGetRequest.onsuccess();
        mockPutRequest.onsuccess();
      }, 0);

      // Should still show the image even if notification fails
      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      });
    });
  });

  describe('Preloading Integration', () => {
    it('should preload next images in background', async () => {
      const imageUrl = 'https://example.com/current.jpg';
      const preloadUrls = [
        'https://example.com/next1.jpg',
        'https://example.com/next2.jpg'
      ];

      // Mock cache misses for all images
      const mockGetRequest = { onsuccess: null, result: null };
      mockObjectStore.get.mockReturnValue(mockGetRequest);

      // Mock successful fetches
      global.fetch.mockResolvedValue({
        ok: true,
        blob: jest.fn().mockResolvedValue(new Blob(['image'], { type: 'image/jpeg' }))
      });

      render(
        <BookCoverImage 
          src={imageUrl} 
          alt="Current Book"
          preloadUrls={preloadUrls}
        />
      );

      setTimeout(() => {
        mockGetRequest.onsuccess();
      }, 0);

      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      });

      // Should eventually preload the next images
      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('next1.jpg')
        );
        expect(global.fetch).toHaveBeenCalledWith(
          expect.stringContaining('next2.jpg')
        );
      }, { timeout: 1000 });
    });
  });

  describe('Performance Scenarios', () => {
    it('should handle rapid image URL changes', async () => {
      const urls = [
        'https://example.com/book1.jpg',
        'https://example.com/book2.jpg',
        'https://example.com/book3.jpg'
      ];

      const mockGetRequest = { onsuccess: null, result: null };
      mockObjectStore.get.mockReturnValue(mockGetRequest);

      global.fetch.mockResolvedValue({
        ok: true,
        blob: jest.fn().mockResolvedValue(new Blob(['image'], { type: 'image/jpeg' }))
      });

      const { rerender } = render(
        <BookCoverImage src={urls[0]} alt="Book 1" />
      );

      // Rapid changes
      rerender(<BookCoverImage src={urls[1]} alt="Book 2" />);
      rerender(<BookCoverImage src={urls[2]} alt="Book 3" />);

      setTimeout(() => {
        mockGetRequest.onsuccess();
      }, 0);

      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      });

      // Should handle the final URL
      expect(global.fetch).toHaveBeenCalledWith(
        expect.stringContaining('book3.jpg')
      );
    });

    it('should cleanup blob URLs to prevent memory leaks', async () => {
      const imageUrl = 'https://example.com/book.jpg';

      const mockGetRequest = { onsuccess: null, result: null };
      mockObjectStore.get.mockReturnValue(mockGetRequest);

      global.fetch.mockResolvedValue({
        ok: true,
        blob: jest.fn().mockResolvedValue(new Blob(['image'], { type: 'image/jpeg' }))
      });

      const { unmount } = render(
        <BookCoverImage src={imageUrl} alt="Test Book" />
      );

      setTimeout(() => {
        mockGetRequest.onsuccess();
      }, 0);

      await waitFor(() => {
        expect(screen.getByRole('img')).toHaveAttribute('src', 'blob:mock-url');
      });

      // Unmount component
      unmount();

      // Should cleanup blob URL
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob:mock-url');
    });
  });
});