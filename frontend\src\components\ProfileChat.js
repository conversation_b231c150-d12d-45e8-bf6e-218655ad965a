import React, { useState, useEffect } from 'react';
import { updateProfileFromChat, getUserProfile } from '../services/api';

const ProfileChat = ({ isOpen, onClose, userId = 1 }) => {
  const [userInput, setUserInput] = useState('');
  const [currentProfile, setCurrentProfile] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadUserProfile();
    }
  }, [isOpen, userId]);

  const loadUserProfile = async () => {
    try {
      setLoading(true);
      const profile = await getUserProfile(userId);
      setCurrentProfile(profile.preference_text || 'No preferences set yet.');
    } catch (error) {
      console.error('Failed to load user profile:', error);
      setCurrentProfile('Error loading profile.');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!userInput.trim()) return;

    try {
      setSubmitting(true);
      const response = await updateProfileFromChat(userId, userInput.trim());
      setCurrentProfile(response.profile);
      setUserInput('');
      
      // Show success message briefly
      setTimeout(() => {
        // Could add a success toast here
      }, 1000);
    } catch (error) {
      console.error('Failed to update profile:', error);
      alert('Failed to update preferences. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-800">Reading Preferences</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ✕
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Current Profile Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Your Current Preferences</h3>
            <div className="bg-gray-50 p-4 rounded-lg border">
              {loading ? (
                <div className="text-gray-500 italic">Loading your preferences...</div>
              ) : (
                <p className="text-gray-700 whitespace-pre-wrap">
                  {currentProfile}
                </p>
              )}
            </div>
          </div>

          {/* Chat Input Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-800 mb-3">Tell us about your reading preferences</h3>
            <p className="text-gray-600 text-sm mb-4">
              Share what you like to read, genres you enjoy, authors you love, or any specific preferences. 
              Our AI will update your profile to give you better recommendations.
            </p>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <textarea
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="For example: I love fantasy novels with strong female protagonists, especially books like The Name of the Wind. I also enjoy contemporary fiction that makes me cry. I'm not a fan of horror or overly technical sci-fi..."
                className="w-full h-32 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={submitting}
              />
              
              <div className="flex gap-3">
                <button
                  type="submit"
                  disabled={!userInput.trim() || submitting}
                  className="flex-1 bg-blue-500 text-white py-3 px-6 rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium"
                >
                  {submitting ? 'Updating Preferences...' : 'Update My Preferences'}
                </button>
                <button
                  type="button"
                  onClick={onClose}
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Close
                </button>
              </div>
            </form>
          </div>

          {/* Help Text */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-semibold text-blue-800 mb-2">💡 Tips for better recommendations:</h4>
            <ul className="text-blue-700 text-sm space-y-1">
              <li>• Mention specific books, authors, or series you've enjoyed</li>
              <li>• Tell us about genres you love or want to avoid</li>
              <li>• Share your mood preferences (light-hearted, emotional, thrilling, etc.)</li>
              <li>• Let us know about content preferences (romance level, violence, etc.)</li>
              <li>• Mention if you prefer certain book lengths or complexity levels</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileChat;