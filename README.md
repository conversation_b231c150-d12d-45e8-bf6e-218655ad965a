# BookTok - AI-Powered Book Discovery Platform

A TikTok-style book discovery platform that uses AI to provide personalized book recommendations based on user preferences and interactions.

## Features

- <� **Watercolor Design**: Beautiful, artistic interface inspired by watercolor paintings
- =� **Swipe Navigation**: Intuitive swipe gestures (touch & mouse support)
- � **FastAPI Backend**: High-performance Python API with SQLAlchemy
- � **React Frontend**: Modern React with Tailwind CSS and Framer Motion
- =� **Book Discovery**: Smart book recommendations
- =� **User Interactions**: Track likes, dislikes, and saved books

## Tech Stack

### Backend
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - Database ORM
- **PostgreSQL/SQLite** - Database
- **Pydantic** - Data validation

### Frontend
- **React 18** - Frontend framework
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **React Spring** - Spring-physics animations
- **@use-gesture/react** - Gesture handling

## Quick Start

If you've already set up the project, use these commands to start the application:

### Start Backend
```bash
cd backend
source venv/bin/activate  # On Windows: venv\Scripts\activate
python main.py
```

### Start Frontend
```bash
cd frontend
npm start
```

The backend will run on `http://localhost:8000` and the frontend on `http://localhost:3000`.

## Getting Started

### Backend Setup

1. Navigate to backend directory:
```bash
cd backend
```

2. Create virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Configure environment:
```bash
cp .env.example .env
```
Edit `.env` and add your OpenRouter API key:
```
OPENROUTER_API_KEY=your-api-key-here
```

5. Initialize the database:
```bash
python migrate_db.py
```

6. Start the FastAPI server:
```bash
python main.py
```

The API will be available at `http://localhost:8000`

### Frontend Setup

1. Navigate to frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

4. Start the development server:
```bash
npm start
```

The app will be available at `http://localhost:3000`

## 📱 Usage

### Getting Started
1. Open the app at http://localhost:3000
2. Use the settings button (⚙️) to set your reading preferences
3. Start swiping through AI-generated book recommendations
4. Add books to your library using the book button (📚)
5. View your library with the library button (📖)

### Interactions
- **Swipe Right / Arrow Right**: Like a book
- **Swipe Left / Arrow Left**: Skip a book  
- **Click Book**: View detailed information
- **Save Button**: Mark for later consideration

### AI Learning
The AI learns from:
- Your swipe interactions (likes/dislikes)
- Books in your personal library
- Explicit preferences shared via chat
- Reading status updates

## 🛠️ API Endpoints

### Recommendations
- `GET /recommendations` - Get ready suggestions for user
- `POST /recommendations/generate` - Generate fresh recommendations

### User Profile
- `GET /profile/{user_id}` - Get user preference profile
- `POST /profile/chat` - Update profile via natural language

### User Library
- `GET /library/{user_id}` - Get user's book library
- `POST /library` - Add book to library
- `PUT /library/{book_id}` - Update book status
- `DELETE /library/{book_id}` - Remove from library

### Interactions
- `POST /interactions` - Record user interaction with book