from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional
import uvicorn
import httpx
import io
import asyncio
import os
from datetime import datetime
from pathlib import Path

from database import get_db, engine
from models import User, UserInteraction, UserProfile, UserBook, BookSuggestion
from schemas import (
    UserInteractionCreate, UserInteractionResponse,
    UserProfileResponse, UserProfileUpdate,
    UserBookCreate, UserBookResponse,
    BookSuggestionResponse,
    ProfileChatRequest, RecommendationRequest
)
from ai_service import RecommendationService
import models

# Import temp_cache with error handling
try:
    from temp_cache import temp_image_cache
except ImportError as e:
    print(f"Warning: Could not import temp_cache: {e}")
    temp_image_cache = None

# Create database tables
models.Base.metadata.create_all(bind=engine)

app = FastAPI(
    title="BookTok API",
    description="A TikTok-style book discovery platform",
    version="1.0.0"
)

# Startup and shutdown event handlers for temp image cache
@app.on_event("startup")
async def startup_event():
    """Initialize temporary image cache on startup"""
    if temp_image_cache:
        await temp_image_cache.start()

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up temporary image cache on shutdown"""
    if temp_image_cache:
        await temp_image_cache.stop()

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for book covers
# Get the directory where this script is located
script_dir = Path(__file__).parent
static_dir = script_dir / "static"

# Create static directory if it doesn't exist
static_dir.mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")

@app.get("/")
def read_root():
    return {"message": "Welcome to BookTok API"}

@app.get("/test-image-download")
async def test_image_download():
    """Test endpoint to verify image downloading works"""
    from image_service import image_service
    
    # Test with a known working Open Library cover
    test_url = "https://covers.openlibrary.org/b/isbn/9780062255655-L.jpg"
    result = await image_service.download_and_optimize_cover(
        cover_url=test_url,
        book_id=99999,
        isbn="9780062255655"
    )
    
    return {
        "test_url": test_url,
        "local_path": result,
        "success": result is not None
    }

@app.get("/recommendations", response_model=List[BookSuggestionResponse])
async def get_recommendations(user_id: int = 1, count: int = 5, background_tasks: BackgroundTasks = BackgroundTasks(), db: Session = Depends(get_db)):
    """Get ready book recommendations for swiping"""
    rec_service = RecommendationService(db)
    
    # Get existing ready suggestions
    suggestions = rec_service.get_ready_suggestions(user_id, count)
    
    # If we have no suggestions at all, generate some immediately
    if len(suggestions) == 0:
        # Generate at least 5 recommendations immediately
        suggestions = await rec_service.generate_recommendations(user_id, max(count, 5))
        # Generate more in background for future requests
        background_tasks.add_task(generate_more_recommendations, user_id, 5, db)
    # If we don't have enough, generate more in background
    elif len(suggestions) < count:
        background_tasks.add_task(generate_more_recommendations, user_id, count - len(suggestions), db)
    
    # Transform cover URLs to include full API base URL
    for suggestion in suggestions[:count]:
        if suggestion.ai_book_data:
            book_data = suggestion.ai_book_data
            
            # Ensure cover_image field exists
            if 'cover_image' not in book_data:
                # If no cover image, try to construct one from ISBN
                if 'isbn' in book_data and book_data['isbn']:
                    book_data['cover_image'] = f"https://covers.openlibrary.org/b/isbn/{book_data['isbn']}-L.jpg"
                else:
                    book_data['cover_image'] = None
            
            # If we have a cover URL, transform it
            cover_url = book_data.get('cover_image')
            if cover_url:
                # If it's a local path, prepend the API base URL
                if cover_url.startswith('/static/'):
                    # Get the request base URL from environment or use default
                    api_base_url = os.getenv('API_BASE_URL', 'http://localhost:8000')
                    book_data['cover_image'] = api_base_url + cover_url
                # If it's an external URL, leave it as is (the frontend will handle it)
                
            # Update the suggestion with modified data
            suggestion.ai_book_data = book_data
    
    return suggestions[:count]  # Return only requested count

@app.post("/recommendations/generate")
async def generate_recommendations(request: RecommendationRequest, db: Session = Depends(get_db)):
    """Generate fresh recommendations for user"""
    rec_service = RecommendationService(db)
    # Handle None user_id by using default value
    user_id = request.user_id if request.user_id is not None else 1
    count = request.count if request.count is not None else 5
    suggestions = await rec_service.generate_recommendations(user_id, count)
    return {"message": f"Generated {len(suggestions)} recommendations", "count": len(suggestions)}

@app.post("/recommendations/refresh")
async def refresh_recommendations(user_id: int = 1, db: Session = Depends(get_db)):
    """Clear existing suggestions and generate fresh ones for testing"""
    # Clear existing suggestions for this user
    db.query(BookSuggestion).filter(BookSuggestion.user_id == user_id).delete()
    db.commit()
    
    rec_service = RecommendationService(db)
    suggestions = await rec_service.generate_recommendations(user_id, 5)
    
    return {
        "message": f"Cleared old suggestions and generated {len(suggestions)} fresh recommendations", 
        "count": len(suggestions),
        "suggestions": [s.ai_book_data for s in suggestions]
    }

async def generate_more_recommendations(user_id: int, count: int, db: Session):
    """Background task to generate more recommendations"""
    rec_service = RecommendationService(db)
    await rec_service.generate_recommendations(user_id, count)

@app.post("/interactions", response_model=UserInteractionResponse)
async def create_interaction(interaction: UserInteractionCreate, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """Record user interaction with a book (like, dislike, save)"""
    db_interaction = UserInteraction(**interaction.dict())
    db.add(db_interaction)
    db.commit()
    db.refresh(db_interaction)
    
    # Update user profile in background after interaction
    # Handle None user_id by using default value
    user_id = interaction.user_id if interaction.user_id is not None else 1
    background_tasks.add_task(update_profile_from_interaction, user_id, db)
    
    return db_interaction

async def update_profile_from_interaction(user_id: int, db: Session):
    """Background task to update user profile after interaction"""
    rec_service = RecommendationService(db)
    await rec_service.update_user_profile_from_interactions(user_id)

# User Profile Management
@app.get("/profile/{user_id}", response_model=UserProfileResponse)
def get_user_profile(user_id: int, db: Session = Depends(get_db)):
    """Get user's preference profile"""
    profile = db.query(UserProfile).filter(UserProfile.user_id == user_id).first()
    if not profile:
        # Create empty profile if doesn't exist
        profile = UserProfile(user_id=user_id, preference_text="")
        db.add(profile)
        db.commit()
        db.refresh(profile)
    return profile

@app.post("/profile/chat")
async def update_profile_from_chat(request: ProfileChatRequest, db: Session = Depends(get_db)):
    """Update user profile from chat input"""
    rec_service = RecommendationService(db)
    # Handle None user_id by using default value
    user_id = request.user_id if request.user_id is not None else 1
    profile = await rec_service.update_profile_from_chat(user_id, request.user_input)
    return {"message": "Profile updated successfully", "profile": profile.preference_text}

@app.put("/profile/{user_id}")
def update_profile_settings(user_id: int, update: UserProfileUpdate, db: Session = Depends(get_db)):
    """Update profile settings"""
    profile = db.query(UserProfile).filter(UserProfile.user_id == user_id).first()
    if not profile:
        raise HTTPException(status_code=404, detail="Profile not found")
    
    # Update profile attributes properly
    update_data = {}
    if update.interaction_history_enabled is not None:
        update_data["interaction_history_enabled"] = update.interaction_history_enabled
    if update.preference_text is not None:
        update_data["preference_text"] = update.preference_text
    
    if update_data:
        update_data["updated_at"] = datetime.utcnow()
        db.query(UserProfile).filter(UserProfile.user_id == user_id).update(update_data)
        db.commit()
    
    return {"message": "Profile updated successfully"}

# User Library Management
@app.get("/library/{user_id}", response_model=List[UserBookResponse])
def get_user_library(user_id: int, db: Session = Depends(get_db)):
    """Get user's book library"""
    books = db.query(UserBook).filter(UserBook.user_id == user_id).all()
    return books

@app.post("/library", response_model=UserBookResponse)
def add_book_to_library(book: UserBookCreate, db: Session = Depends(get_db)):
    """Add a book to user's library"""
    db_book = UserBook(**book.dict())
    db.add(db_book)
    db.commit()
    db.refresh(db_book)
    return db_book

@app.delete("/library/{book_id}")
def remove_book_from_library(book_id: int, db: Session = Depends(get_db)):
    """Remove a book from user's library"""
    book = db.query(UserBook).filter(UserBook.id == book_id).first()
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    db.delete(book)
    db.commit()
    return {"message": "Book removed from library"}

@app.put("/library/{book_id}")
def update_book_status(book_id: int, status: str, db: Session = Depends(get_db)):
    """Update book reading status"""
    book = db.query(UserBook).filter(UserBook.id == book_id).first()
    if not book:
        raise HTTPException(status_code=404, detail="Book not found")
    
    if status not in ['reading', 'finished', 'want_to_read']:
        raise HTTPException(status_code=400, detail="Invalid status")
    
    # Update book status properly using SQLAlchemy update
    db.query(UserBook).filter(UserBook.id == book_id).update({"status": status})
    db.commit()
    return {"message": "Book status updated"}

# Mark suggestions as shown
@app.post("/suggestions/{suggestion_id}/shown")
def mark_suggestion_shown(suggestion_id: int, db: Session = Depends(get_db)):
    """Mark a suggestion as shown to user"""
    rec_service = RecommendationService(db)
    rec_service.mark_suggestion_shown(suggestion_id)
    return {"message": "Suggestion marked as shown"}

# Keep proxy endpoint for potential future use
@app.get("/proxy/image")
async def proxy_image(url: str):
    """Proxy external images to avoid CORS issues"""
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, follow_redirects=True)
            if response.status_code == 200:
                return StreamingResponse(
                    io.BytesIO(response.content),
                    media_type=response.headers.get("content-type", "image/jpeg")
                )
            else:
                raise HTTPException(status_code=response.status_code, detail="Failed to fetch image")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching image: {str(e)}")

# Health check endpoint
@app.get("/health")
def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}

# Temp cache endpoints
@app.get("/api/temp-cache/image")
async def get_temp_cached_image(url: str):
    """Get image from temp cache or fetch it if not cached"""
    if not temp_image_cache:
        raise HTTPException(status_code=503, detail="Temporary cache not available")
    try:
        image_data = await temp_image_cache.get_or_fetch(url)
        if image_data is None:
            raise HTTPException(status_code=404, detail="Image not found")
        
        # Determine content type based on image data
        content_type = "image/jpeg"  # Default
        if image_data.startswith(b'\x89PNG'):
            content_type = "image/png"
        elif image_data.startswith(b'GIF'):
            content_type = "image/gif"
        elif image_data.startswith(b'<svg'):
            content_type = "image/svg+xml"
        
        return StreamingResponse(
            io.BytesIO(image_data),
            media_type=content_type
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching image: {str(e)}")

@app.post("/api/cache/client-cached")
async def mark_client_cached(request: dict):
    """Remove image from server cache when client has cached it"""
    if not temp_image_cache:
        raise HTTPException(status_code=503, detail="Temporary cache not available")
    try:
        image_url = request.get("image_url")
        if not image_url:
            raise HTTPException(status_code=400, detail="image_url is required")
        
        removed = await temp_image_cache.remove(image_url)
        
        return {
            "message": "Image removed from server cache" if removed else "Image was not in cache",
            "removed": removed,
            "image_url": image_url
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error removing from cache: {str(e)}")

@app.get("/api/temp-cache/stats")
async def get_temp_cache_stats():
    """Get temporary cache statistics"""
    if not temp_image_cache:
        raise HTTPException(status_code=503, detail="Temporary cache not available")
    try:
        stats = await temp_image_cache.get_stats()
        return stats
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting cache stats: {str(e)}")

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)