import React, { useState, useEffect } from 'react';
import useImageCache from '../hooks/useImageCache';

const BookCoverImage = ({ 
  src, 
  alt, 
  className = '', 
  enableCache = true,
  showCacheIndicator = false,
  preloadUrls = [],
  onLoad = null,
  onError = null 
}) => {
  const [fallbackError, setFallbackError] = useState(false);
  
  const placeholderUrl = 'https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover';
  
  // Use cache hook if enabled
  const {
    src: cachedSrc,
    isLoading,
    error: cacheError,
    isCached,
    isRefreshing
  } = useImageCache(enableCache ? src : null, {
    fallbackUrl: placeholderUrl,
    preloadUrls,
    onLoad,
    onError
  });

  // Handle non-cached fallback
  const handleImageError = () => {
    console.warn(`Failed to load image: ${src}`);
    setFallbackError(true);
    if (onError) {
      onError({ url: src, error: 'Image load failed' });
    }
  };

  // Reset fallback error when src changes
  useEffect(() => {
    setFallbackError(false);
  }, [src]);
  
  // Determine which source to use
  let imageSrc;
  if (enableCache) {
    imageSrc = cachedSrc || placeholderUrl;
  } else {
    imageSrc = (!src || fallbackError) ? placeholderUrl : src;
  }
  
  return (
    <div className="relative">
      <img 
        src={imageSrc}
        alt={alt}
        className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        onError={!enableCache ? handleImageError : undefined}
      />
      
      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="w-8 h-8 border-4 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
        </div>
      )}
      
      {/* Cache status indicator (optional) */}
      {showCacheIndicator && !isLoading && enableCache && (
        <div className="absolute top-2 right-2 flex items-center gap-1">
          {isCached && (
            <span className="bg-green-500 text-white text-xs px-2 py-1 rounded" title="Cached locally">
              ⚡
            </span>
          )}
          {isRefreshing && (
            <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded animate-pulse" title="Updating cache">
              🔄
            </span>
          )}
        </div>
      )}
      
      {/* Error state */}
      {(cacheError || fallbackError) && !isLoading && imageSrc === placeholderUrl && (
        <div className="absolute bottom-2 left-2 right-2 bg-red-500 bg-opacity-90 text-white text-xs p-2 rounded">
          Failed to load cover image
        </div>
      )}
    </div>
  );
};

export default BookCoverImage;