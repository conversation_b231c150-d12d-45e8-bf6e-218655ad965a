#!/usr/bin/env python3
"""
Migration script to download and cache book covers locally for AI-generated suggestions
"""
import asyncio
from sqlalchemy.orm import Session
from database import SessionLocal, engine
from models import BookSuggestion
from image_service import image_service
import sys
import json

async def migrate_book_covers():
    """Download covers for all book suggestions in the database"""
    db = SessionLocal()
    
    try:
        # Get all book suggestions
        suggestions = db.query(BookSuggestion).all()
        print(f"Found {len(suggestions)} book suggestions to process")
        
        # Prepare data for batch download
        books_data = []
        suggestion_map = {}  # Map temporary ID to suggestion
        
        for idx, suggestion in enumerate(suggestions):
            book_data = suggestion.ai_book_data
            cover_url = book_data.get('cover_image')
            isbn = book_data.get('isbn')
            
            # Skip if no cover URL or if it's already a local path
            if cover_url and not cover_url.startswith('/static/'):
                temp_id = f"suggestion_{suggestion.id}"
                books_data.append({
                    'id': temp_id,
                    'cover_url': cover_url,
                    'isbn': isbn
                })
                suggestion_map[temp_id] = suggestion
        
        if not books_data:
            print("All suggestions already have local covers or no cover URLs")
            return
        
        print(f"Downloading covers for {len(books_data)} book suggestions...")
        
        # Download covers
        results = await image_service.download_multiple_covers(books_data)
        
        # Update database with local paths
        success_count = 0
        for temp_id, local_path in results.items():
            suggestion = suggestion_map.get(temp_id)
            if suggestion and local_path:
                # Update the ai_book_data JSON to include local cover path
                book_data = suggestion.ai_book_data.copy()
                book_data['cover_image'] = local_path
                suggestion.ai_book_data = book_data
                success_count += 1
                print(f"✓ Downloaded cover for: {book_data.get('title', 'Unknown')}")
        
        db.commit()
        print(f"\nSuccessfully downloaded {success_count} covers")
        
        # Report any failures
        failed_count = len(books_data) - success_count
        if failed_count > 0:
            print(f"Failed to download {failed_count} covers")
            
    except Exception as e:
        print(f"Error during migration: {str(e)}")
        db.rollback()
        sys.exit(1)
    finally:
        db.close()

if __name__ == "__main__":
    print("Starting book cover migration...")
    asyncio.run(migrate_book_covers())
    print("Migration completed!")