from pydantic import BaseModel
from typing import Optional, Dict, Any
from datetime import datetime


class UserBase(BaseModel):
    username: str
    email: str
    full_name: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserResponse(UserBase):
    id: int
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserInteractionBase(BaseModel):
    book_data: Dict[str, Any]  # AI book data
    interaction_type: str  # 'like', 'dislike', 'save'

class UserInteractionCreate(UserInteractionBase):
    user_id: Optional[int] = 1  # Default user for demo

class UserInteractionResponse(UserInteractionBase):
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class UserProfileBase(BaseModel):
    preference_text: Optional[str] = None
    interaction_history_enabled: bool = True

class UserProfileCreate(UserProfileBase):
    user_id: int

class UserProfileUpdate(BaseModel):
    preference_text: Optional[str] = None
    interaction_history_enabled: Optional[bool] = None

class UserProfileResponse(UserProfileBase):
    id: int
    user_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class UserBookBase(BaseModel):
    title: str
    author: str
    description: Optional[str] = None
    status: str = 'reading'  # 'reading', 'finished', 'want_to_read'

class UserBookCreate(UserBookBase):
    user_id: Optional[int] = 1  # Default user for demo

class UserBookResponse(UserBookBase):
    id: int
    user_id: int
    created_at: datetime
    
    class Config:
        from_attributes = True

class BookSuggestionBase(BaseModel):
    ai_book_data: Dict[str, Any]  # AI generated book data

class BookSuggestionCreate(BookSuggestionBase):
    user_id: int
    expired_at: Optional[datetime] = None

class BookSuggestionResponse(BookSuggestionBase):
    id: int
    user_id: int
    shown: bool
    expired_at: Optional[datetime] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class ProfileChatRequest(BaseModel):
    user_input: str
    user_id: Optional[int] = 1  # Default user for demo

class RecommendationRequest(BaseModel):
    user_id: Optional[int] = 1  # Default user for demo
    count: int = 5  # Number of recommendations to generate