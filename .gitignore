# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
*.db
*.sqlite
*.sqlite3

# FastAPI
.env

# Node
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# React
/frontend/build/
/frontend/.env
/frontend/.env.local
/frontend/.env.development.local
/frontend/.env.test.local
/frontend/.env.production.local

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Runtime
*.pid
*.seed
*.pid.lock

# Coverage
coverage/
*.lcov

# Static files
/backend/static/covers/*
!/backend/static/covers/.gitkeep