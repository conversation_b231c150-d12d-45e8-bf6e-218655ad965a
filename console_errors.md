react refresh:37 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
manifest.json:1 Manifest: Line: 1, column: 1, Syntax error.
BookCarousel.js:105 Loaded recommendations: (5) [{…}, {…}, {…}, {…}, {…}]
BookCarousel.js:105 Loaded recommendations: (5) [{…}, {…}, {…}, {…}, {…}]
via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover:1 
            
            
           GET https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover net::ERR_NAME_NOT_RESOLVED
Image
commitMount @ react-dom.development.js:11038
commitLayoutEffectOnFiber @ react-dom.development.js:23446
commitLayoutMountEffects_complete @ react-dom.development.js:24727
commitLayoutEffects_begin @ react-dom.development.js:24713
commitLayoutEffects @ react-dom.development.js:24651
commitRootImpl @ react-dom.development.js:26862
commitRoot @ react-dom.development.js:26721
finishConcurrentRender @ react-dom.development.js:26020
performConcurrentWorkOnRoot @ react-dom.development.js:25848
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780062963622.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
useImageCache.js:93 Failed to load image: http://localhost:8000/static/covers/9780062963622.jpg Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
(anonymous) @ useImageCache.js:93
await in (anonymous)
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780593538654.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250838482.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780525428395.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780062963622.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780593538654.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250838482.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780062963622.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780525428395.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780593538654.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
useImageCache.js:93 Failed to load image: http://localhost:8000/static/covers/9780593538654.jpg Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
(anonymous) @ useImageCache.js:93
await in (anonymous)
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250838482.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780525428395.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780062963622.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
useImageCache.js:93 Failed to load image: http://localhost:8000/static/covers/9780062963622.jpg Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
(anonymous) @ useImageCache.js:93
await in (anonymous)
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250838482.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780593538654.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780525428395.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780062963622.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780062963622.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780593538654.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250838482.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780593538654.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
useImageCache.js:93 Failed to load image: http://localhost:8000/static/covers/9780593538654.jpg Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
(anonymous) @ useImageCache.js:93
await in (anonymous)
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
useImageCache.js:93 Failed to load image: http://localhost:8000/static/covers/9781250838482.jpg Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
(anonymous) @ useImageCache.js:93
await in (anonymous)
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780525428395.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250838482.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9780525428395.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9780525428395.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250838482.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250838482.jpg
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250838482.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
getImage @ ImageCacheService.js:165
await in getImage
(anonymous) @ useImageCache.js:54
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
useImageCache.js:93 Failed to load image: http://localhost:8000/static/covers/9781250838482.jpg Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
    at async ImageCacheService.getImage (ImageCacheService.js:165:1)
    at async useImageCache.js:54:1
(anonymous) @ useImageCache.js:93
await in (anonymous)
(anonymous) @ useImageCache.js:123
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:110 
            
            
           GET http://localhost:8000/api/temp-cache/image?url=http%3A%2F%2Flocalhost%3A8000%2Fstatic%2Fcovers%2F9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:110
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:116 Image not in temp cache, fetching directly: http://localhost:8000/static/covers/9781250229165.jpg
ImageCacheService.js:117 
            
            
           GET http://localhost:8000/static/covers/9781250229165.jpg 404 (Not Found)
fetchAndCache @ ImageCacheService.js:117
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ImageCacheService.js:150 Error fetching and caching image: Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
fetchAndCache @ ImageCacheService.js:150
await in fetchAndCache
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
Error: Failed to fetch image: 404
    at ImageCacheService.fetchAndCache (ImageCacheService.js:130:1)
Promise.catch
(anonymous) @ ImageCacheService.js:300
await in (anonymous)
preloadImages @ ImageCacheService.js:294
(anonymous) @ useImageCache.js:129
commitHookEffectListMount @ react-dom.development.js:23189
invokePassiveEffectMountInDEV @ react-dom.development.js:25193
invokeEffectsInDev @ react-dom.development.js:27390
commitDoubleInvokeEffectsInDEV @ react-dom.development.js:27369
flushPassiveEffectsImpl @ react-dom.development.js:27095
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
