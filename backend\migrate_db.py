#!/usr/bin/env python3
"""
Database migration script to clean up old schema and create new tables
"""
import os
from sqlalchemy import create_engine, text
from database import DATABASE_URL
from models import Base

def migrate_database():
    """Drop old tables and create new schema"""
    engine = create_engine(DATABASE_URL)
    
    print("Starting database migration...")
    
    # Drop existing tables
    with engine.connect() as conn:
        print("Dropping old tables...")
        try:
            # Drop tables in correct order (reverse of dependencies)
            conn.execute(text("DROP TABLE IF EXISTS user_interactions"))
            conn.execute(text("DROP TABLE IF EXISTS books"))
            conn.execute(text("DROP TABLE IF EXISTS book_suggestions"))
            conn.execute(text("DROP TABLE IF EXISTS user_books"))
            conn.execute(text("DROP TABLE IF EXISTS user_profiles"))
            conn.execute(text("DROP TABLE IF EXISTS users"))
            conn.commit()
            print("✓ Old tables dropped successfully")
        except Exception as e:
            print(f"Warning: Error dropping tables (may not exist): {e}")
    
    # Create new tables
    print("Creating new tables...")
    Base.metadata.create_all(bind=engine)
    print("✓ New tables created successfully")
    
    # Create default demo user
    from sqlalchemy.orm import sessionmaker
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        from models import User, UserProfile
        
        # Check if demo user exists
        demo_user = session.query(User).filter(User.username == "demo_user").first()
        if not demo_user:
            demo_user = User(
                username="demo_user",
                email="<EMAIL>",
                full_name="Demo User",
                is_active=True
            )
            session.add(demo_user)
            session.commit()
            session.refresh(demo_user)
            print("✓ Demo user created")
        
        # Create empty profile for demo user
        profile = session.query(UserProfile).filter(UserProfile.user_id == demo_user.id).first()
        if not profile:
            profile = UserProfile(
                user_id=demo_user.id,
                preference_text="New user - preferences will be learned from interactions.",
                interaction_history_enabled=True
            )
            session.add(profile)
            session.commit()
            print("✓ Demo user profile created")
            
    except Exception as e:
        print(f"Error creating demo user: {e}")
        session.rollback()
    finally:
        session.close()
    
    print("✅ Database migration completed successfully!")
    print("\nNext steps:")
    print("1. Set up your OpenRouter API key in .env file")
    print("2. Start the backend server: python main.py")
    print("3. Start the frontend: cd frontend && npm start")

if __name__ == "__main__":
    migrate_database()