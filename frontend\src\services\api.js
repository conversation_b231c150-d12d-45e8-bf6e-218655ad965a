import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Recommendations API
export const fetchRecommendations = async (userId = 1, count = 5) => {
  try {
    const response = await api.get(`/recommendations?user_id=${userId}&count=${count}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching recommendations:', error);
    throw error;
  }
};

export const generateRecommendations = async (userId = 1, count = 5) => {
  try {
    const response = await api.post('/recommendations/generate', {
      user_id: userId,
      count: count
    });
    return response.data;
  } catch (error) {
    console.error('Error generating recommendations:', error);
    throw error;
  }
};

export const markSuggestionShown = async (suggestionId) => {
  try {
    const response = await api.post(`/suggestions/${suggestionId}/shown`);
    return response.data;
  } catch (error) {
    console.error('Error marking suggestion as shown:', error);
    throw error;
  }
};

// Interactions API (updated for AI book data)
export const recordInteraction = async (interactionData) => {
  try {
    const response = await api.post('/interactions', interactionData);
    return response.data;
  } catch (error) {
    console.error('Error recording interaction:', error);
    throw error;
  }
};

// User Profile API
export const getUserProfile = async (userId = 1) => {
  try {
    const response = await api.get(`/profile/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw error;
  }
};

export const updateProfileFromChat = async (userId = 1, userInput) => {
  try {
    const response = await api.post('/profile/chat', {
      user_id: userId,
      user_input: userInput
    });
    return response.data;
  } catch (error) {
    console.error('Error updating profile from chat:', error);
    throw error;
  }
};

// User Library API
export const getUserLibrary = async (userId = 1) => {
  try {
    const response = await api.get(`/library/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching user library:', error);
    throw error;
  }
};

export const addBookToLibrary = async (bookData) => {
  try {
    const response = await api.post('/library', bookData);
    return response.data;
  } catch (error) {
    console.error('Error adding book to library:', error);
    throw error;
  }
};

export const removeBookFromLibrary = async (bookId) => {
  try {
    const response = await api.delete(`/library/${bookId}`);
    return response.data;
  } catch (error) {
    console.error('Error removing book from library:', error);
    throw error;
  }
};

export const updateBookStatus = async (bookId, status) => {
  try {
    const response = await api.put(`/library/${bookId}?status=${status}`);
    return response.data;
  } catch (error) {
    console.error('Error updating book status:', error);
    throw error;
  }
};

export default api;