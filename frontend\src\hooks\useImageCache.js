import { useState, useEffect, useRef, useCallback } from 'react';
import ImageCacheService from '../services/ImageCacheService';

const useImageCache = (imageUrl, options = {}) => {
  const {
    fallbackUrl = 'https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover',
    enableBackgroundRefresh = true,
    refreshInterval = 24 * 60 * 60 * 1000, // 24 hours
    onError = null,
    onLoad = null,
    preloadUrls = []
  } = options;

  const [state, setState] = useState({
    src: null,
    isLoading: true,
    error: null,
    isCached: false,
    isRefreshing: false
  });

  const cleanupRef = useRef(null);
  const mountedRef = useRef(true);
  const lastUrlRef = useRef(imageUrl);

  // Cleanup function for blob URLs
  const cleanup = useCallback(() => {
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }
  }, []);

  // Main image loading logic
  const loadImage = useCallback(async (url, isBackgroundRefresh = false) => {
    if (!url) {
      setState({
        src: fallbackUrl,
        isLoading: false,
        error: 'No image URL provided',
        isCached: false,
        isRefreshing: false
      });
      return;
    }

    try {
      if (!isBackgroundRefresh) {
        setState(prev => ({ ...prev, isLoading: true, error: null }));
      } else {
        setState(prev => ({ ...prev, isRefreshing: true }));
      }

      const result = await ImageCacheService.getImage(url);
      
      if (!mountedRef.current) return;

      if (result) {
        // Clean up previous blob URL
        cleanup();
        
        // Store new cleanup function
        cleanupRef.current = result.cleanup;

        setState({
          src: result.url,
          isLoading: false,
          error: null,
          isCached: result.cached,
          isRefreshing: false
        });

        if (onLoad) {
          onLoad({ url, cached: result.cached });
        }

        // Schedule background refresh if enabled and image was cached
        if (enableBackgroundRefresh && result.cached && !isBackgroundRefresh) {
          const age = Date.now() - result.timestamp;
          if (age > refreshInterval) {
            // Refresh in background without affecting current display
            setTimeout(() => {
              if (mountedRef.current) {
                loadImage(url, true);
              }
            }, 100);
          }
        }
      } else {
        throw new Error('Failed to load image');
      }
    } catch (error) {
      console.warn(`Failed to load image: ${url}`, error);
      
      if (!mountedRef.current) return;

      const errorState = {
        src: fallbackUrl,
        isLoading: false,
        error: error.message || 'Failed to load image',
        isCached: false,
        isRefreshing: false
      };

      setState(errorState);

      if (onError) {
        onError({ url, error });
      }
    }
  }, [fallbackUrl, enableBackgroundRefresh, refreshInterval, onLoad, onError, cleanup]);

  // Effect to load image when URL changes
  useEffect(() => {
    if (imageUrl !== lastUrlRef.current) {
      lastUrlRef.current = imageUrl;
      loadImage(imageUrl);
    }
  }, [imageUrl, loadImage]);

  // Initial load
  useEffect(() => {
    loadImage(imageUrl);
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Preload related images
  useEffect(() => {
    if (preloadUrls && preloadUrls.length > 0) {
      ImageCacheService.preloadImages(preloadUrls);
    }
  }, [preloadUrls]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      cleanup();
    };
  }, [cleanup]);

  // Manual refresh function
  const refresh = useCallback(() => {
    loadImage(imageUrl, false);
  }, [imageUrl, loadImage]);

  // Cache management functions
  const clearThisImage = useCallback(async () => {
    if (imageUrl) {
      await ImageCacheService.removeCachedImage(imageUrl);
      refresh();
    }
  }, [imageUrl, refresh]);

  const getCacheStats = useCallback(() => {
    return ImageCacheService.getCacheStats();
  }, []);

  return {
    src: state.src,
    isLoading: state.isLoading,
    error: state.error,
    isCached: state.isCached,
    isRefreshing: state.isRefreshing,
    refresh,
    clearCache: clearThisImage,
    getCacheStats
  };
};

// Hook for managing multiple images at once
export const useImageCacheBatch = (imageUrls) => {
  const [results, setResults] = useState({});
  const mountedRef = useRef(true);

  useEffect(() => {
    const loadImages = async () => {
      const newResults = {};
      
      for (const url of imageUrls) {
        if (!url) continue;
        
        try {
          const result = await ImageCacheService.getImage(url);
          if (mountedRef.current && result) {
            newResults[url] = {
              src: result.url,
              cached: result.cached,
              cleanup: result.cleanup
            };
          }
        } catch (error) {
          newResults[url] = {
            src: null,
            error: error.message,
            cached: false
          };
        }
      }

      if (mountedRef.current) {
        setResults(newResults);
      }
    };

    loadImages();

    return () => {
      mountedRef.current = false;
      // Cleanup all blob URLs
      Object.values(results).forEach(result => {
        if (result.cleanup) {
          result.cleanup();
        }
      });
    };
  }, [imageUrls]); // eslint-disable-line react-hooks/exhaustive-deps

  return results;
};

export default useImageCache;