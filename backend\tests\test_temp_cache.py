import pytest
import pytest_asyncio
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from io import Bytes<PERSON>
from PIL import Image
import aiohttp
from aioresponses import aioresponses

import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from temp_cache import TemporaryImageCache


@pytest.fixture
def sample_image_data():
    """Create sample image data for testing."""
    # Create a small test image
    img = Image.new('RGB', (100, 100), color='red')
    buffer = BytesIO()
    img.save(buffer, format='JPEG')
    return buffer.getvalue()


@pytest.fixture
def large_image_data():
    """Create large image data for testing optimization."""
    # Create a large test image (600x800)
    img = Image.new('RGB', (600, 800), color='blue')
    buffer = BytesIO()
    img.save(buffer, format='JPEG', quality=100)
    return buffer.getvalue()


@pytest_asyncio.fixture
async def cache():
    """Create a cache instance for testing."""
    cache = TemporaryImageCache(max_items=3, ttl_seconds=2)
    await cache.start()
    yield cache
    await cache.stop()


class TestBasicCaching:
    """Test basic caching functionality."""
    
    @pytest.mark.asyncio
    async def test_cache_miss_and_fetch(self, cache, sample_image_data):
        """Test cache miss triggers fetch and caching."""
        test_url = "http://example.com/image.jpg"
        
        with aioresponses() as mocked:
            mocked.get(test_url, body=sample_image_data, 
                      headers={'Content-Type': 'image/jpeg'})
            
            # First fetch should be a cache miss
            result = await cache.get_or_fetch(test_url)
            
            assert result is not None
            data, content_type = result
            assert data == sample_image_data
            assert content_type == 'image/jpeg'
            
            # Verify it's now in cache
            assert test_url in cache.cache
    
    @pytest.mark.asyncio
    async def test_cache_hit(self, cache, sample_image_data):
        """Test cache hit returns cached data without fetching."""
        test_url = "http://example.com/image.jpg"
        
        # Manually add to cache
        await cache._add_to_cache(test_url, sample_image_data, 'image/jpeg')
        
        # This should be a cache hit - no HTTP request made
        with aioresponses() as mocked:
            # No URL mocked - should not make request
            result = await cache.get_or_fetch(test_url)
            
            assert result is not None
            data, content_type = result
            assert data == sample_image_data
            assert content_type == 'image/jpeg'
    
    @pytest.mark.asyncio
    async def test_multiple_urls(self, cache, sample_image_data):
        """Test caching multiple different URLs."""
        urls = [
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg"
        ]
        
        with aioresponses() as mocked:
            for url in urls:
                mocked.get(url, body=sample_image_data,
                          headers={'Content-Type': 'image/jpeg'})
            
            # Fetch all URLs
            for url in urls:
                result = await cache.get_or_fetch(url)
                assert result is not None
            
            # All should be in cache
            assert len(cache.cache) == 3
            for url in urls:
                assert url in cache.cache


class TestTTLExpiration:
    """Test TTL expiration behavior."""
    
    @pytest.mark.asyncio
    async def test_ttl_expiration(self, sample_image_data):
        """Test that cached items expire after TTL."""
        # Create cache with short TTL
        cache = TemporaryImageCache(max_items=5, ttl_seconds=0.5)
        await cache.start()
        
        try:
            test_url = "http://example.com/image.jpg"
            
            with aioresponses() as mocked:
                # First request
                mocked.get(test_url, body=sample_image_data,
                          headers={'Content-Type': 'image/jpeg'})
                
                result1 = await cache.get_or_fetch(test_url)
                assert result1 is not None
                
                # Wait for TTL to expire
                await asyncio.sleep(0.6)
                
                # Second request should fetch again due to expiration
                mocked.get(test_url, body=sample_image_data,
                          headers={'Content-Type': 'image/jpeg'})
                
                result2 = await cache.get_or_fetch(test_url)
                assert result2 is not None
                
                # Should have made 2 requests total
                assert len(mocked.requests) == 2
        finally:
            await cache.stop()
    
    @pytest.mark.asyncio
    async def test_periodic_cleanup(self, sample_image_data):
        """Test periodic cleanup of expired entries."""
        # Create cache with short TTL
        cache = TemporaryImageCache(max_items=5, ttl_seconds=0.1)
        
        # Mock the cleanup interval to be shorter
        with patch.object(cache, '_periodic_cleanup') as mock_cleanup:
            async def quick_cleanup():
                await asyncio.sleep(0.2)
                await cache._periodic_cleanup.__wrapped__(cache)
            
            mock_cleanup.side_effect = quick_cleanup
            await cache.start()
            
            try:
                # Add items to cache
                await cache._add_to_cache("url1", sample_image_data, "image/jpeg")
                await cache._add_to_cache("url2", sample_image_data, "image/jpeg")
                
                assert len(cache.cache) == 2
                
                # Wait for cleanup to run
                await asyncio.sleep(0.3)
                
                # Cache should be empty after cleanup
                assert len(cache.cache) == 0
            finally:
                await cache.stop()


class TestLRUEviction:
    """Test LRU eviction when max_items is reached."""
    
    @pytest.mark.asyncio
    async def test_lru_eviction_on_max_items(self, cache, sample_image_data):
        """Test that oldest items are evicted when cache is full."""
        urls = [
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg",
            "http://example.com/image4.jpg"  # This should evict image1
        ]
        
        with aioresponses() as mocked:
            for url in urls:
                mocked.get(url, body=sample_image_data,
                          headers={'Content-Type': 'image/jpeg'})
            
            # Fetch first 3 URLs (fills cache)
            for url in urls[:3]:
                await cache.get_or_fetch(url)
            
            assert len(cache.cache) == 3
            assert urls[0] in cache.cache
            
            # Fetch 4th URL (should evict first)
            await cache.get_or_fetch(urls[3])
            
            assert len(cache.cache) == 3
            assert urls[0] not in cache.cache  # First URL evicted
            assert urls[3] in cache.cache  # New URL added
    
    @pytest.mark.asyncio
    async def test_lru_access_order_update(self, cache, sample_image_data):
        """Test that accessing cached items updates LRU order."""
        urls = [
            "http://example.com/image1.jpg",
            "http://example.com/image2.jpg",
            "http://example.com/image3.jpg"
        ]
        
        # Manually add to cache
        for i, url in enumerate(urls):
            await cache._add_to_cache(url, sample_image_data, "image/jpeg")
        
        # Access first URL again (moves to end)
        await cache.get_or_fetch(urls[0])
        
        # Add new URL - should evict image2 (now oldest)
        new_url = "http://example.com/image4.jpg"
        with aioresponses() as mocked:
            mocked.get(new_url, body=sample_image_data,
                      headers={'Content-Type': 'image/jpeg'})
            await cache.get_or_fetch(new_url)
        
        assert urls[0] in cache.cache  # Still in cache (recently accessed)
        assert urls[1] not in cache.cache  # Evicted (was oldest)
        assert urls[2] in cache.cache
        assert new_url in cache.cache


class TestImageOptimization:
    """Test image optimization for large images."""
    
    @pytest.mark.asyncio
    async def test_large_image_optimization(self, cache, large_image_data):
        """Test that large images are optimized."""
        test_url = "http://example.com/large-image.jpg"
        
        # Verify our test image is large
        assert len(large_image_data) > 500 * 1024
        
        with aioresponses() as mocked:
            mocked.get(test_url, body=large_image_data,
                      headers={'Content-Type': 'image/jpeg'})
            
            result = await cache.get_or_fetch(test_url)
            assert result is not None
            
            optimized_data, content_type = result
            
            # Optimized image should be smaller
            assert len(optimized_data) < len(large_image_data)
            
            # Verify it's still a valid image
            img = Image.open(BytesIO(optimized_data))
            assert img.width <= 400  # Max width from optimization
    
    @pytest.mark.asyncio
    async def test_rgba_image_conversion(self, cache):
        """Test RGBA image conversion to RGB."""
        # Create RGBA image
        img = Image.new('RGBA', (200, 200), (255, 0, 0, 128))
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        rgba_data = buffer.getvalue()
        
        test_url = "http://example.com/rgba-image.png"
        
        # Make it large enough to trigger optimization
        large_rgba = rgba_data * 100  # Repeat to make it large
        
        with aioresponses() as mocked:
            mocked.get(test_url, body=large_rgba,
                      headers={'Content-Type': 'image/png'})
            
            result = await cache.get_or_fetch(test_url)
            assert result is not None
            
            optimized_data, content_type = result
            
            # Should be converted to JPEG
            assert content_type == 'image/jpeg'
            
            # Verify it's RGB (not RGBA)
            img = Image.open(BytesIO(optimized_data))
            assert img.mode == 'RGB'


class TestCacheOperations:
    """Test cache management operations."""
    
    @pytest.mark.asyncio
    async def test_remove_from_cache(self, cache, sample_image_data):
        """Test removing specific items from cache."""
        test_url = "http://example.com/image.jpg"
        
        # Add to cache
        await cache._add_to_cache(test_url, sample_image_data, "image/jpeg")
        assert test_url in cache.cache
        
        # Remove it
        await cache.remove(test_url)
        assert test_url not in cache.cache
        assert test_url not in cache.access_order
    
    @pytest.mark.asyncio
    async def test_remove_nonexistent(self, cache):
        """Test removing non-existent item doesn't raise error."""
        await cache.remove("http://example.com/nonexistent.jpg")
        # Should not raise any exception
    
    @pytest.mark.asyncio
    async def test_clear_cache(self, cache, sample_image_data):
        """Test clearing entire cache."""
        # Add multiple items
        urls = ["http://example.com/img1.jpg", "http://example.com/img2.jpg"]
        for url in urls:
            await cache._add_to_cache(url, sample_image_data, "image/jpeg")
        
        assert len(cache.cache) == 2
        
        # Clear cache
        await cache.clear()
        
        assert len(cache.cache) == 0
        assert len(cache.access_order) == 0
    
    @pytest.mark.asyncio
    async def test_cache_statistics(self, cache, sample_image_data):
        """Test cache statistics."""
        # Add items with known sizes
        await cache._add_to_cache("url1", sample_image_data, "image/jpeg")
        await cache._add_to_cache("url2", sample_image_data * 2, "image/jpeg")
        
        stats = await cache.get_stats()
        
        assert stats["count"] == 2
        assert stats["total_size_bytes"] == len(sample_image_data) * 3
        assert stats["max_items"] == 3
        assert stats["ttl_seconds"] == 2
        assert "url1" in stats["cached_urls"]
        assert "url2" in stats["cached_urls"]


class TestConcurrentAccess:
    """Test concurrent access handling."""
    
    @pytest.mark.asyncio
    async def test_concurrent_fetches_same_url(self, cache, sample_image_data):
        """Test multiple concurrent fetches of same URL."""
        test_url = "http://example.com/image.jpg"
        fetch_count = 0
        
        async def mock_fetch(*args, **kwargs):
            nonlocal fetch_count
            fetch_count += 1
            await asyncio.sleep(0.1)  # Simulate network delay
            return sample_image_data, "image/jpeg"
        
        with patch.object(cache, '_fetch_and_optimize_image', mock_fetch):
            # Launch multiple concurrent fetches
            tasks = [cache.get_or_fetch(test_url) for _ in range(5)]
            results = await asyncio.gather(*tasks)
            
            # All should succeed
            assert all(r is not None for r in results)
            
            # Should have fetched multiple times (no deduplication in current impl)
            # This is expected behavior - each concurrent request fetches
            assert fetch_count >= 1
    
    @pytest.mark.asyncio
    async def test_concurrent_different_urls(self, cache, sample_image_data):
        """Test concurrent fetches of different URLs."""
        urls = [f"http://example.com/image{i}.jpg" for i in range(5)]
        
        with aioresponses() as mocked:
            for url in urls:
                mocked.get(url, body=sample_image_data,
                          headers={'Content-Type': 'image/jpeg'})
            
            # Fetch all URLs concurrently
            tasks = [cache.get_or_fetch(url) for url in urls]
            results = await asyncio.gather(*tasks)
            
            # All should succeed
            assert all(r is not None for r in results)
            
            # Only first 3 should be in cache (max_items=3)
            assert len(cache.cache) == 3
    
    @pytest.mark.asyncio
    async def test_concurrent_modifications(self, cache, sample_image_data):
        """Test concurrent cache modifications (add/remove/clear)."""
        
        async def add_items():
            for i in range(5):
                await cache._add_to_cache(f"url{i}", sample_image_data, "image/jpeg")
                await asyncio.sleep(0.01)
        
        async def remove_items():
            await asyncio.sleep(0.02)
            for i in range(5):
                await cache.remove(f"url{i}")
                await asyncio.sleep(0.01)
        
        async def get_stats():
            stats = []
            for _ in range(10):
                stat = await cache.get_stats()
                stats.append(stat["count"])
                await asyncio.sleep(0.01)
            return stats
        
        # Run operations concurrently
        results = await asyncio.gather(
            add_items(),
            remove_items(),
            get_stats(),
            return_exceptions=True
        )
        
        # Should not have any exceptions
        assert not any(isinstance(r, Exception) for r in results)


class TestErrorHandling:
    """Test error handling for failed operations."""
    
    @pytest.mark.asyncio
    async def test_http_error_response(self, cache):
        """Test handling of HTTP error responses."""
        test_url = "http://example.com/404.jpg"
        
        with aioresponses() as mocked:
            mocked.get(test_url, status=404)
            
            result = await cache.get_or_fetch(test_url)
            assert result is None
    
    @pytest.mark.asyncio
    async def test_network_timeout(self, cache):
        """Test handling of network timeouts."""
        test_url = "http://example.com/timeout.jpg"
        
        with aioresponses() as mocked:
            # Simulate timeout by raising exception
            mocked.get(test_url, exception=asyncio.TimeoutError())
            
            result = await cache.get_or_fetch(test_url)
            assert result is None
    
    @pytest.mark.asyncio
    async def test_invalid_image_data(self, cache):
        """Test handling of invalid image data."""
        test_url = "http://example.com/invalid.jpg"
        
        with aioresponses() as mocked:
            # Return invalid image data
            mocked.get(test_url, body=b"not an image",
                      headers={'Content-Type': 'image/jpeg'})
            
            result = await cache.get_or_fetch(test_url)
            # Should still return the data (optimization fails gracefully)
            assert result is not None
            data, content_type = result
            assert data == b"not an image"
    
    @pytest.mark.asyncio
    async def test_connection_error(self, cache):
        """Test handling of connection errors."""
        test_url = "http://example.com/connection-error.jpg"
        
        with aioresponses() as mocked:
            mocked.get(test_url, exception=aiohttp.ClientError("Connection failed"))
            
            result = await cache.get_or_fetch(test_url)
            assert result is None
    
    @pytest.mark.asyncio
    async def test_image_optimization_failure(self, cache):
        """Test graceful handling of image optimization failures."""
        test_url = "http://example.com/corrupt.jpg"
        
        # Create corrupted "large" data
        corrupt_data = b"corrupted image data" * 50000
        
        with aioresponses() as mocked:
            mocked.get(test_url, body=corrupt_data,
                      headers={'Content-Type': 'image/jpeg'})
            
            with patch('PIL.Image.open', side_effect=Exception("Invalid image")):
                result = await cache.get_or_fetch(test_url)
                
                # Should still return original data when optimization fails
                assert result is not None
                data, content_type = result
                assert data == corrupt_data


class TestStartStop:
    """Test cache lifecycle management."""
    
    @pytest.mark.asyncio
    async def test_start_stop_lifecycle(self):
        """Test proper start and stop of cache."""
        cache = TemporaryImageCache()
        
        # Initially no cleanup task
        assert cache._cleanup_task is None
        
        # Start creates cleanup task
        await cache.start()
        assert cache._cleanup_task is not None
        assert not cache._cleanup_task.done()
        
        # Stop cancels cleanup task
        await cache.stop()
        assert cache._cleanup_task is None
    
    @pytest.mark.asyncio
    async def test_multiple_start_calls(self):
        """Test multiple start calls don't create multiple tasks."""
        cache = TemporaryImageCache()
        
        await cache.start()
        task1 = cache._cleanup_task
        
        await cache.start()
        task2 = cache._cleanup_task
        
        # Should be the same task
        assert task1 is task2
        
        await cache.stop()
    
    @pytest.mark.asyncio
    async def test_stop_without_start(self):
        """Test stop without start doesn't raise error."""
        cache = TemporaryImageCache()
        await cache.stop()  # Should not raise


@pytest.mark.asyncio
async def test_integration_workflow(sample_image_data, large_image_data):
    """Test a complete workflow with various operations."""
    cache = TemporaryImageCache(max_items=3, ttl_seconds=1)
    await cache.start()
    
    try:
        # URLs to test
        small_url = "http://example.com/small.jpg"
        large_url = "http://example.com/large.jpg"
        urls = [f"http://example.com/img{i}.jpg" for i in range(5)]
        
        with aioresponses() as mocked:
            # Mock various responses
            mocked.get(small_url, body=sample_image_data,
                      headers={'Content-Type': 'image/jpeg'})
            mocked.get(large_url, body=large_image_data,
                      headers={'Content-Type': 'image/jpeg'})
            for url in urls:
                mocked.get(url, body=sample_image_data,
                          headers={'Content-Type': 'image/jpeg'})
            
            # 1. Fetch and cache small image
            result = await cache.get_or_fetch(small_url)
            assert result is not None
            
            # 2. Fetch and optimize large image
            result = await cache.get_or_fetch(large_url)
            assert result is not None
            data, _ = result
            assert len(data) < len(large_image_data)
            
            # 3. Fill cache to capacity
            for url in urls[:2]:
                await cache.get_or_fetch(url)
            
            stats = await cache.get_stats()
            assert stats["count"] == 3
            
            # 4. Test cache hit
            mocked.get(small_url, body=b"should not fetch",
                      headers={'Content-Type': 'image/jpeg'})
            result = await cache.get_or_fetch(small_url)
            data, _ = result
            assert data == sample_image_data  # Got cached version
            
            # 5. Remove item and verify
            await cache.remove(small_url)
            assert small_url not in cache.cache
            
            # 6. Wait for TTL expiration
            await asyncio.sleep(1.1)
            
            # Re-mock for new fetch
            mocked.get(large_url, body=b"new data",
                      headers={'Content-Type': 'image/jpeg'})
            
            # Should fetch new data after expiration
            result = await cache.get_or_fetch(large_url)
            data, _ = result
            assert data == b"new data"
            
            # 7. Clear cache
            await cache.clear()
            stats = await cache.get_stats()
            assert stats["count"] == 0
            
    finally:
        await cache.stop()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])