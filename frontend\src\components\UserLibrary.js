import React, { useState, useEffect } from 'react';
import { getUserLibrary, removeBookFromLibrary, updateBookStatus } from '../services/api';

const UserLibrary = ({ isOpen, onClose, userId = 1 }) => {
  const [books, setBooks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState('all');

  useEffect(() => {
    if (isOpen) {
      loadLibrary();
    }
  }, [isOpen, userId]);

  const loadLibrary = async () => {
    try {
      setLoading(true);
      const libraryBooks = await getUserLibrary(userId);
      setBooks(libraryBooks);
    } catch (error) {
      console.error('Failed to load user library:', error);
      setBooks([]);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusChange = async (bookId, newStatus) => {
    try {
      await updateBookStatus(bookId, newStatus);
      setBooks(prevBooks =>
        prevBooks.map(book =>
          book.id === bookId ? { ...book, status: newStatus } : book
        )
      );
    } catch (error) {
      console.error('Failed to update book status:', error);
      alert('Failed to update book status. Please try again.');
    }
  };

  const handleRemoveBook = async (bookId, bookTitle) => {
    if (!window.confirm(`Are you sure you want to remove "${bookTitle}" from your library?`)) {
      return;
    }

    try {
      await removeBookFromLibrary(bookId);
      setBooks(prevBooks => prevBooks.filter(book => book.id !== bookId));
    } catch (error) {
      console.error('Failed to remove book:', error);
      alert('Failed to remove book. Please try again.');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'reading':
        return 'bg-blue-100 text-blue-800';
      case 'finished':
        return 'bg-green-100 text-green-800';
      case 'want_to_read':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status) => {
    switch (status) {
      case 'reading':
        return 'Currently Reading';
      case 'finished':
        return 'Finished';
      case 'want_to_read':
        return 'Want to Read';
      default:
        return status;
    }
  };

  const filteredBooks = selectedStatus === 'all' 
    ? books 
    : books.filter(book => book.status === selectedStatus);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-800">My Library</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ✕
          </button>
        </div>

        {/* Filter Tabs */}
        <div className="px-6 pt-4">
          <div className="flex space-x-4 border-b border-gray-200">
            {[
              { key: 'all', label: 'All Books', count: books.length },
              { key: 'reading', label: 'Currently Reading', count: books.filter(b => b.status === 'reading').length },
              { key: 'finished', label: 'Finished', count: books.filter(b => b.status === 'finished').length },
              { key: 'want_to_read', label: 'Want to Read', count: books.filter(b => b.status === 'want_to_read').length }
            ].map(tab => (
              <button
                key={tab.key}
                onClick={() => setSelectedStatus(tab.key)}
                className={`pb-3 px-1 font-medium text-sm border-b-2 transition-colors ${
                  selectedStatus === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="text-gray-500">Loading your library...</div>
            </div>
          ) : filteredBooks.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-gray-500 mb-4">
                {selectedStatus === 'all' 
                  ? 'Your library is empty. Add some books to get started!'
                  : `No books in "${getStatusLabel(selectedStatus)}" status.`
                }
              </div>
              <button
                onClick={onClose}
                className="text-blue-500 hover:text-blue-600 font-medium"
              >
                Add Books to Library
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredBooks.map((book) => (
                <div key={book.id} className="bg-gray-50 rounded-lg p-4 flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-800 text-lg">{book.title}</h3>
                    <p className="text-gray-600 italic mb-2">by {book.author}</p>
                    {book.description && (
                      <p className="text-gray-700 text-sm mb-3 line-clamp-2">{book.description}</p>
                    )}
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(book.status)}`}>
                        {getStatusLabel(book.status)}
                      </span>
                      <span className="text-gray-500 text-xs">
                        Added {new Date(book.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex flex-col gap-2 ml-4">
                    {/* Status Dropdown */}
                    <select
                      value={book.status}
                      onChange={(e) => handleStatusChange(book.id, e.target.value)}
                      className="text-xs px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="want_to_read">Want to Read</option>
                      <option value="reading">Currently Reading</option>
                      <option value="finished">Finished</option>
                    </select>
                    
                    {/* Remove Button */}
                    <button
                      onClick={() => handleRemoveBook(book.id, book.title)}
                      className="text-xs text-red-500 hover:text-red-700 px-2 py-1 border border-red-300 hover:border-red-500 rounded transition-colors"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 pb-6">
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="font-semibold text-blue-800 mb-2">📖 Your Reading Journey</h4>
            <p className="text-blue-700 text-sm">
              Books in your library help our AI understand your reading preferences. 
              The more books you add and rate, the better our recommendations become!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserLibrary;