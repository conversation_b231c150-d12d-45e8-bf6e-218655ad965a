import { useEffect, useRef } from 'react';

/**
 * Hook to preload images for better performance
 * @param {Array} images - Array of image URLs to preload
 * @param {number} preloadCount - Number of images to preload ahead (default: 3)
 */
const useImagePreloader = (images, preloadCount = 3) => {
  const preloadedImages = useRef({});
  
  useEffect(() => {
    if (!images || images.length === 0) return;
    
    // Function to preload a single image
    const preloadImage = (url) => {
      if (!url || preloadedImages.current[url]) return;
      
      const img = new Image();
      img.src = url;
      
      img.onload = () => {
        preloadedImages.current[url] = true;
        console.log(`Preloaded: ${url}`);
      };
      
      img.onerror = () => {
        console.warn(`Failed to preload: ${url}`);
      };
    };
    
    // Preload the specified number of images
    images.slice(0, preloadCount).forEach(url => {
      preloadImage(url);
    });
    
    // Cleanup function
    return () => {
      // Images will be cached by the browser, no cleanup needed
    };
  }, [images, preloadCount]);
  
  return preloadedImages.current;
};

export default useImagePreloader;