import os
import json
import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential
from sqlalchemy.orm import Session
from models import UserProfile, UserInteraction, UserBook, BookSuggestion
from database import get_db
from image_service import image_service

class OpenAIClient:
    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        self.base_url = "https://api.openai.com/v1"
        self.model = os.getenv("OPENAI_MODEL", "gpt-4.1-mini")
        
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable is required")
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _make_request(self, messages: List[Dict[str, str]], max_tokens: int = 1000, response_format: Optional[Dict[str, str]] = None) -> str:
        """Make a request to OpenAI API with retry logic"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": 0.7
        }
        
        if response_format:
            payload["response_format"] = response_format
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            data = response.json()
            return data["choices"][0]["message"]["content"]
    
    async def generate_profile_from_interactions(self, interactions: List[UserInteraction], current_profile: str = "") -> str:
        """Generate or update user profile based on interactions"""
        interaction_summary = []
        
        for interaction in interactions[-20:]:  # Use last 20 interactions
            book_data = interaction.book_data
            interaction_summary.append({
                "action": interaction.interaction_type,
                "book": {
                    "title": book_data.get("title", "Unknown"),
                    "author": book_data.get("author", "Unknown"),
                    "genre": book_data.get("genre", "Unknown"),
                    "description": book_data.get("description", "")[:200]  # Truncate for efficiency
                }
            })
        
        messages = [
            {
                "role": "system",
                "content": """You are a book recommendation expert. Analyze user interactions to create a detailed but concise user preference profile. Focus on:
- Preferred genres and themes
- Author styles and writing preferences  
- Book length and complexity preferences
- Content preferences (romance, violence, fantasy elements, etc.)
- Reading motivations (entertainment, learning, escapism, etc.)

Keep the profile under 500 words, detailed but concise. Be specific and actionable for future recommendations."""
            },
            {
                "role": "user", 
                "content": f"""
Current user profile: {current_profile or "No existing profile"}

Recent interactions:
{json.dumps(interaction_summary, indent=2)}

Generate an updated user preference profile based on this data. If there's an existing profile, update it with new insights from the interactions. Focus on patterns in liked vs disliked books.
"""
            }
        ]
        
        return await self._make_request(messages, max_tokens=800)
    
    async def update_profile_from_chat(self, current_profile: str, user_input: str) -> str:
        """Update user profile based on chat input"""
        messages = [
            {
                "role": "system",
                "content": "You are a book recommendation expert. Update the user's preference profile based on their direct input. Integrate their feedback with existing preferences, keeping the profile detailed but concise (under 500 words)."
            },
            {
                "role": "user",
                "content": f"""
Current profile: {current_profile or "No existing profile"}

User feedback: {user_input}

Update the profile incorporating this new information. Maintain existing preferences unless contradicted by the new input.
"""
            }
        ]
        
        return await self._make_request(messages, max_tokens=800)
    
    async def generate_book_recommendations(self, user_profile: str, user_books: List[UserBook], interaction_history: Optional[List[UserInteraction]] = None, count: int = 5) -> List[Dict[str, Any]]:
        """Generate book recommendations based on user profile and library"""
        
        user_library = [{"title": book.title, "author": book.author, "status": book.status} for book in user_books]
        
        recent_interactions = []
        if interaction_history:
            for interaction in interaction_history[-10:]:  # Last 10 interactions
                book_data = interaction.book_data
                recent_interactions.append({
                    "action": interaction.interaction_type,
                    "title": book_data.get("title", "Unknown"),
                    "author": book_data.get("author", "Unknown")
                })
        
        messages = [
            {
                "role": "system",
                "content": f"""You are a book recommendation expert. Generate {count} diverse book recommendations based on the user's profile and reading history. 

You must respond with a JSON object containing a "recommendations" array with this exact format:
{{
  "recommendations": [
    {{
      "title": "Book Title",
      "author": "Author Name", 
      "description": "2-3 sentence compelling description focusing on why this user would enjoy it",
      "genre": "Primary Genre",
      "why_recommended": "Brief explanation of why this matches user preferences",
      "publication_year": 2023,
      "estimated_pages": 320,
      "isbn": "9780123456789",
      "cover_image": "https://covers.openlibrary.org/b/isbn/9780123456789-L.jpg"
    }}
  ]
}}

IMPORTANT: For each book, you MUST include:
- A valid ISBN-13 (starting with 978 or 979)
- The cover_image URL using the format: https://covers.openlibrary.org/b/isbn/[ISBN]-L.jpg

Ensure variety in genres, authors, and publication dates. Avoid books the user already has or recently interacted with."""
            },
            {
                "role": "user",
                "content": f"""
User profile: {user_profile}

User's library: {json.dumps(user_library, indent=2)}

Recent interactions: {json.dumps(recent_interactions, indent=2)}

Generate {count} book recommendations in the specified JSON format. Focus on books that match the user's preferences but offer some variety.
"""
            }
        ]
        
        try:
            response = await self._make_request(
                messages, 
                max_tokens=2000, 
                response_format={"type": "json_object"}
            )
            data = json.loads(response)
            return data.get("recommendations", [])
        except (json.JSONDecodeError, KeyError) as e:
            print(f"JSON parsing error: {e}")
            print(f"Response: {response}")
            # Return fallback recommendations
            return await self._generate_fallback_recommendations(count)
    
    async def _generate_fallback_recommendations(self, count: int) -> List[Dict[str, Any]]:
        """Generate basic recommendations using AI when main recommendation system fails"""
        messages = [
            {
                "role": "system",
                "content": f"""You are a book recommendation expert. Generate {count} diverse, popular book recommendations suitable for a general audience.

You must respond with a JSON object containing a "recommendations" array with this exact format:
{{
  "recommendations": [
    {{
      "title": "Book Title",
      "author": "Author Name", 
      "description": "2-3 sentence compelling description",
      "genre": "Primary Genre",
      "why_recommended": "Brief explanation of appeal",
      "publication_year": 2023,
      "estimated_pages": 320,
      "isbn": "9780123456789",
      "cover_image": "https://covers.openlibrary.org/b/isbn/9780123456789-L.jpg"
    }}
  ]
}}

IMPORTANT: For each book, you MUST include:
- A valid ISBN-13 (starting with 978 or 979)
- The cover_image URL using the format: https://covers.openlibrary.org/b/isbn/[ISBN]-L.jpg

Focus on well-reviewed, popular books from the last 5-10 years across different genres (fiction, non-fiction, mystery, romance, sci-fi, fantasy, etc.)."""
            },
            {
                "role": "user",
                "content": f"Generate {count} diverse book recommendations for a general audience. Include variety in genres, authors, and publication years."
            }
        ]
        
        try:
            response = await self._make_request(
                messages, 
                max_tokens=1500, 
                response_format={"type": "json_object"}
            )
            data = json.loads(response)
            return data.get("recommendations", [])
        except Exception as e:
            print(f"Fallback recommendation generation failed: {e}")
            # If even the fallback AI fails, return empty list
            return []

# Singleton instance
ai_client = OpenAIClient()

class RecommendationService:
    def __init__(self, db: Session):
        self.db = db
    
    async def update_user_profile_from_interactions(self, user_id: int) -> UserProfile:
        """Update user profile based on recent interactions"""
        # Get or create user profile
        profile = self.db.query(UserProfile).filter(UserProfile.user_id == user_id).first()
        if not profile:
            profile = UserProfile(user_id=user_id, preference_text="")
            self.db.add(profile)
        
        # Get recent interactions
        interactions = self.db.query(UserInteraction).filter(
            UserInteraction.user_id == user_id
        ).order_by(UserInteraction.created_at.desc()).limit(50).all()
        
        if interactions:
            updated_profile_text = await ai_client.generate_profile_from_interactions(
                interactions, profile.preference_text or ""
            )
            profile.preference_text = updated_profile_text
            profile.updated_at = datetime.utcnow()
            self.db.commit()
        
        return profile
    
    async def update_profile_from_chat(self, user_id: int, user_input: str) -> UserProfile:
        """Update user profile from chat input"""
        profile = self.db.query(UserProfile).filter(UserProfile.user_id == user_id).first()
        if not profile:
            profile = UserProfile(user_id=user_id, preference_text="")
            self.db.add(profile)
        
        updated_profile_text = await ai_client.update_profile_from_chat(
            profile.preference_text or "", user_input
        )
        profile.preference_text = updated_profile_text
        profile.updated_at = datetime.utcnow()
        self.db.commit()
        
        return profile
    
    async def generate_recommendations(self, user_id: int, count: int = 5) -> List[BookSuggestion]:
        """Generate fresh recommendations for user"""
        # Get user profile
        profile = self.db.query(UserProfile).filter(UserProfile.user_id == user_id).first()
        if not profile or not profile.preference_text:
            # Update profile first if it doesn't exist or is empty
            profile = await self.update_user_profile_from_interactions(user_id)
        
        # Get user's library
        user_books = self.db.query(UserBook).filter(UserBook.user_id == user_id).all()
        
        # Get interaction history if enabled
        interaction_history = None
        if profile and profile.interaction_history_enabled:
            interaction_history = self.db.query(UserInteraction).filter(
                UserInteraction.user_id == user_id
            ).order_by(UserInteraction.created_at.desc()).limit(30).all()
        
        # Generate recommendations
        # If profile is still empty (new user), provide a generic preference text
        preference_text = profile.preference_text if (profile and profile.preference_text) else "New reader exploring various genres. Looking for popular, well-reviewed books across different categories including fiction, non-fiction, mystery, romance, and science fiction. Open to discovering new authors and genres."
        
        recommendations = await ai_client.generate_book_recommendations(
            preference_text, user_books, interaction_history, count
        )
        
        # Download covers for recommendations (optional - don't fail if download fails)
        try:
            books_data = []
            for i, rec in enumerate(recommendations):
                if rec.get('cover_image') and rec.get('isbn'):
                    books_data.append({
                        'id': i,  # Temporary ID for mapping
                        'cover_url': rec['cover_image'],
                        'isbn': rec.get('isbn')
                    })
            
            # Download covers if we have any
            if books_data:
                print(f"Attempting to download {len(books_data)} book covers...")
                cover_results = await image_service.download_multiple_covers(books_data)
                # Update recommendations with local cover paths
                for i, local_path in cover_results.items():
                    if local_path:
                        # Replace external URL with local path
                        recommendations[i]['cover_image'] = local_path
                        print(f"Successfully cached cover for book {i}")
        except Exception as e:
            # Don't fail recommendation generation if image download fails
            print(f"Warning: Failed to download some book covers: {str(e)}")
            # Keep the original URLs - frontend can still display them
        
        # Store suggestions in database
        suggestions = []
        expired_at = datetime.utcnow() + timedelta(hours=24)  # Expire after 24 hours
        
        for rec in recommendations:
            suggestion = BookSuggestion(
                user_id=user_id,
                ai_book_data=rec,
                expired_at=expired_at
            )
            self.db.add(suggestion)
            suggestions.append(suggestion)
        
        self.db.commit()
        return suggestions
    
    def get_ready_suggestions(self, user_id: int, count: int = 5) -> List[BookSuggestion]:
        """Get ready suggestions for user, removing expired ones"""
        # Clean up expired suggestions
        self.db.query(BookSuggestion).filter(
            BookSuggestion.user_id == user_id,
            BookSuggestion.expired_at < datetime.utcnow()
        ).delete()
        
        # Get available suggestions
        suggestions = self.db.query(BookSuggestion).filter(
            BookSuggestion.user_id == user_id,
            BookSuggestion.shown == False
        ).limit(count).all()
        
        self.db.commit()
        return suggestions
    
    def mark_suggestion_shown(self, suggestion_id: int):
        """Mark a suggestion as shown to user"""
        suggestion = self.db.query(BookSuggestion).filter(BookSuggestion.id == suggestion_id).first()
        if suggestion:
            suggestion.shown = True
            self.db.commit()