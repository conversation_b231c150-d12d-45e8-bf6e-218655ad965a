import React, { useState, useEffect, useCallback } from 'react';
import { useDrag } from '@use-gesture/react';
import { useSpring, animated } from '@react-spring/web';
import BookCard from './BookCard';
import BookCoverImage from './BookCoverImage';
import { fetchRecommendations, recordInteraction, markSuggestionShown, generateRecommendations, getUserProfile } from '../services/api';

// Book Details Modal Component
const BookDetailsModal = ({ book, isOpen, onClose }) => {
  if (!isOpen || !book) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full max-h-[90vh] overflow-y-auto p-6">
        <div className="flex justify-between items-start mb-4">
          <h2 className="text-2xl font-bold text-gray-800">Book Details</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ✕
          </button>
        </div>
        
        <div className="text-center mb-6">
          <BookCoverImage 
            src={book.cover_image} 
            alt={book.title}
            className="w-32 h-48 object-cover rounded-lg mx-auto mb-4 shadow-lg"
          />
          <h3 className="text-xl font-bold text-gray-800 mb-2">{book.title}</h3>
          <p className="text-lg text-gray-600 italic mb-4">by {book.author}</p>
        </div>
        
        <div className="space-y-4">
          <div>
            <h4 className="font-semibold text-gray-800 mb-2">Description</h4>
            <p className="text-gray-700 leading-relaxed">{book.description}</p>
          </div>
          
          {book.genre && (
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Genre</h4>
              <p className="text-gray-700">{book.genre}</p>
            </div>
          )}
          
          {book.publication_year && (
            <div>
              <h4 className="font-semibold text-gray-800 mb-2">Publication Year</h4>
              <p className="text-gray-700">{book.publication_year}</p>
            </div>
          )}
        </div>
        
        <div className="flex gap-3 mt-6">
          <button 
            onClick={onClose}
            className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
          >
            Close
          </button>
          <button 
            onClick={() => {
              // Handle add to library logic here
              onClose();
            }}
            className="flex-1 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition-colors"
          >
            Add to Library
          </button>
        </div>
      </div>
    </div>
  );
};

const BookCarousel = () => {
  const [suggestions, setSuggestions] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [showBookDetails, setShowBookDetails] = useState(false);
  const [generatingRecommendations, setGeneratingRecommendations] = useState(false);
  const [hasUserProfile, setHasUserProfile] = useState(true);
  const [userInteractions, setUserInteractions] = useState({
    liked: [],
    skipped: [],
    moreInfo: []
  });
  
  // Spring animation for card position
  const [{ x, rotate, scale }, api] = useSpring(() => ({
    x: 0,
    rotate: 0,
    scale: 1,
  }));

  useEffect(() => {
    loadRecommendations();
  }, []);

  const loadRecommendations = async () => {
    try {
      const data = await fetchRecommendations(1, 5); // Default user_id=1, count=5
      console.log('Loaded recommendations:', data);
      setSuggestions(data);
      
      // Check if user has a profile
      if (data.length === 0) {
        try {
          const profile = await getUserProfile(1);
          setHasUserProfile(!!profile.preference_text);
        } catch (error) {
          console.error('Failed to check user profile:', error);
        }
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Failed to load recommendations:', error);
      setLoading(false);
    }
  };
  
  const handleGenerateRecommendations = async () => {
    setGeneratingRecommendations(true);
    try {
      await generateRecommendations(1, 10); // Generate 10 recommendations
      // Reload recommendations after generation
      await loadRecommendations();
    } catch (error) {
      console.error('Failed to generate recommendations:', error);
    } finally {
      setGeneratingRecommendations(false);
    }
  };
  
  // Get next book URLs for preloading
  const getNextBookUrls = (fromIndex) => {
    const urls = [];
    for (let i = 1; i <= 3; i++) {
      const nextIndex = (fromIndex + i) % suggestions.length;
      const coverUrl = suggestions[nextIndex]?.ai_book_data?.cover_image;
      if (coverUrl) urls.push(coverUrl);
    }
    return urls;
  };

  const handleSkip = async () => {
    if (suggestions.length === 0) return;
    
    const currentSuggestion = suggestions[currentIndex];
    const bookData = currentSuggestion.ai_book_data;
    
    try {
      await recordInteraction({
        book_data: bookData,
        interaction_type: 'dislike'
      });
      
      await markSuggestionShown(currentSuggestion.id);
      
      setUserInteractions(prev => ({
        ...prev,
        skipped: [...prev.skipped, currentSuggestion.id]
      }));
    } catch (error) {
      console.error('Failed to record skip interaction:', error);
    }
    
    // Move to next suggestion
    setTimeout(() => {
      moveToNext();
    }, 300);
  };

  const handleShowDetails = async () => {
    setShowBookDetails(true);
    const currentSuggestion = suggestions[currentIndex];
    
    try {
      await recordInteraction({
        book_data: currentSuggestion.ai_book_data,
        interaction_type: 'save'
      });
      
      setUserInteractions(prev => ({
        ...prev,
        moreInfo: [...prev.moreInfo, currentSuggestion.id]
      }));
    } catch (error) {
      console.error('Failed to record save interaction:', error);
    }
  };

  const handleLike = async () => {
    if (suggestions.length === 0) return;
    
    const currentSuggestion = suggestions[currentIndex];
    const bookData = currentSuggestion.ai_book_data;
    
    try {
      await recordInteraction({
        book_data: bookData,
        interaction_type: 'like'
      });
      
      await markSuggestionShown(currentSuggestion.id);
      
      setUserInteractions(prev => ({
        ...prev,
        liked: [...prev.liked, currentSuggestion.id]
      }));
    } catch (error) {
      console.error('Failed to record interaction:', error);
    }
    
    // Move to next suggestion
    setTimeout(() => {
      moveToNext();
    }, 300);
  };

  const bind = useDrag(({ active, movement: [mx], direction: [xDir], velocity }) => {
    const trigger = velocity > 0.2;
    const dir = xDir < 0 ? -1 : 1;
    
    if (!active && trigger) {
      // Swipe triggered
      if (dir === 1) {
        handleLike();
      } else {
        handleSkip();
      }
    }
    
    api.start({
      x: active ? mx : 0,
      rotate: active ? mx / 10 : 0,
      scale: active ? 1 - Math.abs(mx) / 1000 : 1,
      immediate: active,
    });
  });

  const moveToNext = () => {
    const nextIndex = (currentIndex + 1) % suggestions.length;
    setCurrentIndex(nextIndex);
    api.start({ x: 0, rotate: 0, scale: 1 });
  };

  const goToNext = useCallback(() => {
    moveToNext();
  }, [currentIndex, suggestions.length]);

  const goToPrev = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + suggestions.length) % suggestions.length);
  }, [suggestions.length]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === 'ArrowLeft') {
        goToPrev();
      } else if (e.key === 'ArrowRight') {
        goToNext();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [goToNext, goToPrev]);

  if (loading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-10">
        <div className="text-white text-xl">Loading books...</div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-10 p-4">
        <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 max-w-md w-full text-center">
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-white mb-3">
              {hasUserProfile ? "Building Your Recommendations" : "Welcome to BookTok!"}
            </h2>
            <p className="text-white/80">
              {hasUserProfile 
                ? "We need a moment to curate personalized book recommendations just for you. Your reading preferences are being analyzed to find the perfect matches."
                : "To get started with personalized recommendations, we need to learn about your reading preferences. You can start by exploring some books or telling us what you like to read!"}
            </p>
          </div>
          
          <div className="space-y-3">
            <button
              onClick={handleGenerateRecommendations}
              disabled={generatingRecommendations}
              className="w-full bg-watercolor-purple text-white py-3 px-6 rounded-lg font-semibold hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {generatingRecommendations 
                ? "Generating Recommendations..." 
                : "Generate Recommendations"}
            </button>
            
            {!hasUserProfile && (
              <button
                onClick={() => window.location.href = '/profile'}
                className="w-full bg-watercolor-blue/20 text-white py-3 px-6 rounded-lg font-semibold hover:bg-watercolor-blue/30 transition-colors border border-white/20"
              >
                Set Reading Preferences
              </button>
            )}
          </div>
          
          <p className="text-white/60 text-sm mt-6">
            Tip: The more you interact with books, the better our recommendations become!
          </p>
        </div>
      </div>
    );
  }

  const currentSuggestion = suggestions[currentIndex];
  const currentBook = currentSuggestion?.ai_book_data;

  return (
    <>
      <div className="fixed inset-0 flex items-center justify-center z-10">
        <div className="relative w-full max-w-7xl h-[80vh] max-h-[600px] flex items-center justify-center gap-8 px-4">
          
          {/* Left side card */}
          <div className="flex-shrink-0 w-48 md:w-56 h-[65%] relative">
            <div className="w-full h-full opacity-60 transition-all duration-500 ease-out cursor-pointer hover:opacity-80 hover:scale-105"
                 onClick={() => setCurrentIndex((currentIndex - 1 + suggestions.length) % suggestions.length)}>
              <BookCard
                book={suggestions[(currentIndex - 1 + suggestions.length) % suggestions.length]?.ai_book_data}
                onLike={() => {}}
                onDislike={() => {}}
                onSave={() => {}}
                nextBookUrls={getNextBookUrls((currentIndex - 1 + suggestions.length) % suggestions.length)}
              />
            </div>
            
            {/* Left arrow button */}
            <button 
              onClick={() => setCurrentIndex((currentIndex - 1 + suggestions.length) % suggestions.length)}
              className="absolute top-1/2 -right-4 transform -translate-y-1/2 w-10 h-10 bg-white/20 hover:bg-white/30 backdrop-blur-md rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 border border-white/30 shadow-lg hover:shadow-xl"
            >
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>
          
          {/* Center card (current) */}
          <div className="flex-shrink-0 w-[90%] max-w-sm h-full relative">
            <animated.div
              {...bind()}
              style={{
                x,
                rotate,
                scale,
                touchAction: 'none',
              }}
              className="w-full h-full cursor-grab active:cursor-grabbing relative z-10 drop-shadow-2xl"
            >
              <BookCard
                book={currentBook}
                onLike={handleLike}
                onDislike={handleSkip}
                onSave={handleShowDetails}
                nextBookUrls={getNextBookUrls(currentIndex)}
              />
            </animated.div>
          </div>
          
          {/* Right side card */}
          <div className="flex-shrink-0 w-48 md:w-56 h-[65%] relative">
            <div className="w-full h-full opacity-60 transition-all duration-500 ease-out cursor-pointer hover:opacity-80 hover:scale-105"
                 onClick={() => setCurrentIndex((currentIndex + 1) % suggestions.length)}>
              <BookCard
                book={suggestions[(currentIndex + 1) % suggestions.length]?.ai_book_data}
                onLike={() => {}}
                onDislike={() => {}}
                onSave={() => {}}
                nextBookUrls={getNextBookUrls((currentIndex + 1) % suggestions.length)}
              />
            </div>
            
            {/* Right arrow button */}
            <button 
              onClick={() => setCurrentIndex((currentIndex + 1) % suggestions.length)}
              className="absolute top-1/2 -left-4 transform -translate-y-1/2 w-10 h-10 bg-white/20 hover:bg-white/30 backdrop-blur-md rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 border border-white/30 shadow-lg hover:shadow-xl"
            >
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>

        {/* Navigation dots */}
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 flex gap-2 z-20">
          {suggestions.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentIndex 
                  ? 'bg-white scale-125' 
                  : 'bg-white/50'
              }`}
            />
          ))}
        </div>

        {/* Swipe hint */}
        <div className="fixed bottom-24 left-1/2 transform -translate-x-1/2 text-white/80 text-sm z-20 animate-pulse-gentle">
          Swipe or use arrow keys • AI-powered recommendations
        </div>
      </div>

      {/* Book Details Modal */}
      <BookDetailsModal
        book={currentBook}
        isOpen={showBookDetails}
        onClose={() => setShowBookDetails(false)}
      />
    </>
  );
};

export default BookCarousel;