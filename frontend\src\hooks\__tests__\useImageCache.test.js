import { renderHook, act, waitFor } from '@testing-library/react';
import useImageCache, { useImageCacheBatch } from '../useImageCache';
import ImageCacheService from '../../services/ImageCacheService';

// Mock the ImageCacheService
jest.mock('../../services/ImageCacheService', () => ({
  __esModule: true,
  default: {
    getImage: jest.fn(),
    preloadImages: jest.fn(),
    removeCachedImage: jest.fn(),
    getCacheStats: jest.fn()
  }
}));

describe('useImageCache', () => {
  const mockCleanup = jest.fn();
  const defaultCachedResult = {
    url: 'blob:mock-cached-url',
    cached: true,
    timestamp: Date.now(),
    cleanup: mockCleanup
  };

  const defaultFetchedResult = {
    url: 'blob:mock-fetched-url',
    cached: false,
    timestamp: Date.now(),
    cleanup: mockCleanup
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('initial loading', () => {
    it('should start with loading state', () => {
      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);
      
      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      expect(result.current.isLoading).toBe(true);
      expect(result.current.src).toBeNull();
      expect(result.current.error).toBeNull();
    });

    it('should load cached image successfully', async () => {
      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);
      
      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.src).toBe('blob:mock-cached-url');
      expect(result.current.isCached).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it('should handle no URL provided', async () => {
      const { result } = renderHook(() => 
        useImageCache(null)
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.src).toBe('https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover');
      expect(result.current.error).toBe('No image URL provided');
    });

    it('should use custom fallback URL', async () => {
      const customFallback = 'https://custom-fallback.com/image.png';
      
      const { result } = renderHook(() => 
        useImageCache(null, { fallbackUrl: customFallback })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.src).toBe(customFallback);
    });
  });

  describe('error handling', () => {
    it('should handle image loading errors', async () => {
      const error = new Error('Failed to load');
      ImageCacheService.getImage.mockRejectedValue(error);
      
      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.src).toBe('https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover');
      expect(result.current.error).toBe('Failed to load');
      expect(result.current.isCached).toBe(false);
    });

    it('should call onError callback on error', async () => {
      const error = new Error('Network error');
      const onError = jest.fn();
      ImageCacheService.getImage.mockRejectedValue(error);
      
      renderHook(() => 
        useImageCache('https://example.com/image.jpg', { onError })
      );

      await waitFor(() => {
        expect(onError).toHaveBeenCalledWith({
          url: 'https://example.com/image.jpg',
          error
        });
      });
    });

    it('should handle null result from service', async () => {
      ImageCacheService.getImage.mockResolvedValue(null);
      
      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      expect(result.current.src).toBe('https://via.placeholder.com/200x300/E6E6E6/333333?text=No+Cover');
      expect(result.current.error).toBe('Failed to load image');
    });
  });

  describe('callbacks', () => {
    it('should call onLoad callback when image loads', async () => {
      const onLoad = jest.fn();
      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);
      
      renderHook(() => 
        useImageCache('https://example.com/image.jpg', { onLoad })
      );

      await waitFor(() => {
        expect(onLoad).toHaveBeenCalledWith({
          url: 'https://example.com/image.jpg',
          cached: true
        });
      });
    });
  });

  describe('URL changes', () => {
    it('should reload when URL changes', async () => {
      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);
      
      const { result, rerender } = renderHook(
        ({ url }) => useImageCache(url),
        { initialProps: { url: 'https://example.com/image1.jpg' } }
      );

      await waitFor(() => {
        expect(result.current.src).toBe('blob:mock-cached-url');
      });

      // Change URL
      ImageCacheService.getImage.mockResolvedValue({
        ...defaultCachedResult,
        url: 'blob:mock-cached-url-2'
      });

      rerender({ url: 'https://example.com/image2.jpg' });

      await waitFor(() => {
        expect(result.current.src).toBe('blob:mock-cached-url-2');
      });

      expect(ImageCacheService.getImage).toHaveBeenCalledTimes(2);
    });

    it('should cleanup previous blob URL on change', async () => {
      const cleanup1 = jest.fn();
      const cleanup2 = jest.fn();
      
      ImageCacheService.getImage
        .mockResolvedValueOnce({
          url: 'blob:url1',
          cached: true,
          timestamp: Date.now(),
          cleanup: cleanup1
        })
        .mockResolvedValueOnce({
          url: 'blob:url2',
          cached: true,
          timestamp: Date.now(),
          cleanup: cleanup2
        });
      
      const { rerender } = renderHook(
        ({ url }) => useImageCache(url),
        { initialProps: { url: 'https://example.com/image1.jpg' } }
      );

      await waitFor(() => {
        expect(ImageCacheService.getImage).toHaveBeenCalled();
      });

      rerender({ url: 'https://example.com/image2.jpg' });

      await waitFor(() => {
        expect(cleanup1).toHaveBeenCalled();
      });
    });
  });

  describe('background refresh', () => {
    it('should refresh old cached images in background', async () => {
      const oldTimestamp = Date.now() - (25 * 60 * 60 * 1000); // 25 hours old
      const cachedResult = {
        url: 'blob:cached',
        cached: true,
        timestamp: oldTimestamp,
        cleanup: mockCleanup
      };

      ImageCacheService.getImage
        .mockResolvedValueOnce(cachedResult)
        .mockResolvedValueOnce(defaultFetchedResult);

      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg', {
          enableBackgroundRefresh: true,
          refreshInterval: 24 * 60 * 60 * 1000
        })
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Fast-forward timers to trigger background refresh
      act(() => {
        jest.advanceTimersByTime(200);
      });

      await waitFor(() => {
        expect(ImageCacheService.getImage).toHaveBeenCalledTimes(2);
      });

      expect(result.current.isRefreshing).toBe(false);
    });

    it('should not refresh when background refresh is disabled', async () => {
      const oldTimestamp = Date.now() - (25 * 60 * 60 * 1000);
      const cachedResult = {
        url: 'blob:cached',
        cached: true,
        timestamp: oldTimestamp,
        cleanup: mockCleanup
      };

      ImageCacheService.getImage.mockResolvedValueOnce(cachedResult);

      renderHook(() => 
        useImageCache('https://example.com/image.jpg', {
          enableBackgroundRefresh: false
        })
      );

      await waitFor(() => {
        expect(ImageCacheService.getImage).toHaveBeenCalled();
      });

      act(() => {
        jest.advanceTimersByTime(200);
      });

      expect(ImageCacheService.getImage).toHaveBeenCalledTimes(1);
    });

    it('should show refreshing state during background refresh', async () => {
      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);

      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      // Manually trigger a background refresh
      await act(async () => {
        ImageCacheService.getImage.mockImplementation(() => 
          new Promise(resolve => setTimeout(() => resolve(defaultFetchedResult), 100))
        );
        await result.current.refresh();
      });

      // Should show refreshing state briefly
      expect(result.current.isRefreshing).toBe(false); // After completion
    });
  });

  describe('preloading', () => {
    it('should preload provided URLs', async () => {
      const preloadUrls = [
        'https://example.com/next1.jpg',
        'https://example.com/next2.jpg'
      ];

      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);

      renderHook(() => 
        useImageCache('https://example.com/image.jpg', { preloadUrls })
      );

      await waitFor(() => {
        expect(ImageCacheService.preloadImages).toHaveBeenCalledWith(preloadUrls);
      });
    });

    it('should update preload when URLs change', async () => {
      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);

      const { rerender } = renderHook(
        ({ preloadUrls }) => useImageCache('https://example.com/image.jpg', { preloadUrls }),
        { initialProps: { preloadUrls: ['url1'] } }
      );

      await waitFor(() => {
        expect(ImageCacheService.preloadImages).toHaveBeenCalledWith(['url1']);
      });

      rerender({ preloadUrls: ['url2', 'url3'] });

      await waitFor(() => {
        expect(ImageCacheService.preloadImages).toHaveBeenCalledWith(['url2', 'url3']);
      });
    });
  });

  describe('manual refresh', () => {
    it('should allow manual refresh', async () => {
      ImageCacheService.getImage
        .mockResolvedValueOnce(defaultCachedResult)
        .mockResolvedValueOnce(defaultFetchedResult);

      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      await waitFor(() => {
        expect(result.current.src).toBe('blob:mock-cached-url');
      });

      await act(async () => {
        await result.current.refresh();
      });

      expect(result.current.src).toBe('blob:mock-fetched-url');
      expect(ImageCacheService.getImage).toHaveBeenCalledTimes(2);
    });
  });

  describe('cache management', () => {
    it('should clear current image from cache', async () => {
      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);
      ImageCacheService.removeCachedImage.mockResolvedValue(true);

      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      await act(async () => {
        await result.current.clearCache();
      });

      expect(ImageCacheService.removeCachedImage).toHaveBeenCalledWith('https://example.com/image.jpg');
      expect(ImageCacheService.getImage).toHaveBeenCalledTimes(2); // Initial + refresh after clear
    });

    it('should get cache statistics', async () => {
      const mockStats = {
        count: 10,
        totalSizeMB: '25.5',
        maxSizeMB: '50.0',
        utilization: '51.0'
      };

      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);
      ImageCacheService.getCacheStats.mockResolvedValue(mockStats);

      const { result } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false);
      });

      const stats = await result.current.getCacheStats();
      expect(stats).toEqual(mockStats);
    });
  });

  describe('cleanup', () => {
    it('should cleanup blob URL on unmount', async () => {
      ImageCacheService.getImage.mockResolvedValue(defaultCachedResult);

      const { unmount } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      await waitFor(() => {
        expect(ImageCacheService.getImage).toHaveBeenCalled();
      });

      unmount();

      expect(mockCleanup).toHaveBeenCalled();
    });

    it('should handle component unmounting during load', async () => {
      let resolvePromise;
      const promise = new Promise(resolve => { resolvePromise = resolve; });
      
      ImageCacheService.getImage.mockReturnValue(promise);

      const { unmount } = renderHook(() => 
        useImageCache('https://example.com/image.jpg')
      );

      // Unmount before promise resolves
      unmount();

      // Resolve promise after unmount
      act(() => {
        resolvePromise(defaultCachedResult);
      });

      // Should not cause any errors or state updates
    });
  });
});

describe('useImageCacheBatch', () => {
  const cleanup1 = jest.fn();
  const cleanup2 = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should load multiple images', async () => {
    ImageCacheService.getImage
      .mockResolvedValueOnce({
        url: 'blob:url1',
        cached: true,
        cleanup: cleanup1
      })
      .mockResolvedValueOnce({
        url: 'blob:url2',
        cached: false,
        cleanup: cleanup2
      });

    const urls = ['https://example.com/1.jpg', 'https://example.com/2.jpg'];
    
    const { result } = renderHook(() => useImageCacheBatch(urls));

    await waitFor(() => {
      expect(Object.keys(result.current).length).toBe(2);
    });

    expect(result.current['https://example.com/1.jpg']).toEqual({
      src: 'blob:url1',
      cached: true,
      cleanup: cleanup1
    });

    expect(result.current['https://example.com/2.jpg']).toEqual({
      src: 'blob:url2',
      cached: false,
      cleanup: cleanup2
    });
  });

  it('should handle errors in batch loading', async () => {
    ImageCacheService.getImage
      .mockResolvedValueOnce({
        url: 'blob:url1',
        cached: true,
        cleanup: cleanup1
      })
      .mockRejectedValueOnce(new Error('Failed to load'));

    const urls = ['https://example.com/1.jpg', 'https://example.com/2.jpg'];
    
    const { result } = renderHook(() => useImageCacheBatch(urls));

    await waitFor(() => {
      expect(Object.keys(result.current).length).toBe(2);
    });

    expect(result.current['https://example.com/1.jpg'].src).toBe('blob:url1');
    expect(result.current['https://example.com/2.jpg']).toEqual({
      src: null,
      error: 'Failed to load',
      cached: false
    });
  });

  it('should skip null/undefined URLs', async () => {
    const urls = ['https://example.com/1.jpg', null, undefined, ''];
    
    ImageCacheService.getImage.mockResolvedValue({
      url: 'blob:url1',
      cached: true,
      cleanup: cleanup1
    });

    const { result } = renderHook(() => useImageCacheBatch(urls));

    await waitFor(() => {
      expect(Object.keys(result.current).length).toBe(1);
    });

    expect(ImageCacheService.getImage).toHaveBeenCalledTimes(1);
    expect(ImageCacheService.getImage).toHaveBeenCalledWith('https://example.com/1.jpg');
  });

  it('should cleanup all blob URLs on unmount', async () => {
    ImageCacheService.getImage
      .mockResolvedValueOnce({
        url: 'blob:url1',
        cached: true,
        cleanup: cleanup1
      })
      .mockResolvedValueOnce({
        url: 'blob:url2',
        cached: false,
        cleanup: cleanup2
      });

    const urls = ['https://example.com/1.jpg', 'https://example.com/2.jpg'];
    
    const { unmount } = renderHook(() => useImageCacheBatch(urls));

    await waitFor(() => {
      expect(ImageCacheService.getImage).toHaveBeenCalledTimes(2);
    });

    unmount();

    expect(cleanup1).toHaveBeenCalled();
    expect(cleanup2).toHaveBeenCalled();
  });
});