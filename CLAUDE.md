# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Backend (FastAPI + SQLAlchemy)
- **Setup**: `cd backend && python -m venv venv && source venv/bin/activate && pip install -r requirements.txt`
- **Run server**: `cd backend && python main.py` (runs on http://localhost:8000)
- **Database setup**: `cd backend && python seed_data.py` (creates sample books and users)
- **API docs**: Available at http://localhost:8000/docs (Swagger) and http://localhost:8000/redoc

### Frontend (React + Tailwind CSS)
- **Setup**: `cd frontend && npm install`
- **Development**: `cd frontend && npm start` (runs on http://localhost:3000)
- **Build**: `cd frontend && npm run build`
- **Test**: `cd frontend && npm test`

## Architecture Overview

### Backend Architecture
The FastAPI backend follows a clean architecture pattern:

- **main.py**: FastAPI app initialization, CORS setup, and route definitions
- **models.py**: SQLAlchemy ORM models (Book, User, UserInteraction) with relationships
- **schemas.py**: Pydantic models for request/response validation and serialization
- **database.py**: Database connection and session management (defaults to SQLite, configurable to PostgreSQL)
- **seed_data.py**: Sample data loader for development

**Key Design Patterns**:
- Dependency injection for database sessions (`Depends(get_db)`)
- Pydantic schemas separate from ORM models for clean API contracts
- Relationship-based data modeling (Books ↔ UserInteractions ↔ Users)

### Frontend Architecture
React application with advanced gesture handling and animations:

- **BookCarousel.js**: Main swipe container using `@use-gesture/react` for drag gestures and `react-spring` for physics-based animations
- **BookCard.js**: Individual book display with Framer Motion animations for interactions
- **WatercolorBackground.js**: Static decorative background with CSS-based watercolor effects
- **services/api.js**: Axios-based API client with centralized endpoint management

**Key Design Patterns**:
- Spring physics animations for natural swipe motion (x, rotate, scale transforms)
- Gesture velocity detection for swipe triggers (threshold: velocity > 0.2)
- Layered card stack with depth perception (opacity and scale variations)
- Keyboard navigation support (arrow keys) alongside touch/mouse gestures

### Styling System
Custom Tailwind CSS configuration with watercolor theme:

- **Custom color palette**: watercolor.yellow/orange/blue/purple/pink
- **Animation system**: 5 custom keyframe animations (watercolorFlow, float, pulseGentle, watercolorPulse, borderFlow)
- **Responsive design**: Mobile-first with breakpoints for larger screens
- **Typography**: Georgia serif font for artistic feel

### Data Flow
1. **Book Loading**: Frontend fetches books from `/books` endpoint on mount
2. **Swipe Detection**: `useDrag` hook detects gestures and triggers `handleInteraction`
3. **Interaction Recording**: Each swipe/button press sends interaction to `/interactions` endpoint
4. **State Management**: React useState for books array and current index, spring state for animations
5. **Navigation**: Circular array navigation with keyboard and gesture support

### Database Schema
- **Books**: Core content with metadata (title, author, description, cover_image, genre, rating)
- **Users**: User accounts (currently simplified with demo user)
- **UserInteractions**: Junction table tracking user actions ('like', 'dislike', 'save') with timestamps

### API Design
RESTful endpoints with clear separation of concerns:
- `GET /books` - Paginated book listing
- `POST /interactions` - User action tracking
- `GET /books/discover` - Recommendation engine placeholder
- Static file serving for book covers at `/static`

## Environment Configuration

### Backend Environment
- `DATABASE_URL`: Database connection string (defaults to SQLite)
- Copy `backend/.env.example` to `backend/.env` for local development

### Frontend Environment  
- `REACT_APP_API_URL`: Backend API URL (defaults to http://localhost:8000)
- Copy `frontend/.env.example` to `frontend/.env` for local development

## Key Libraries and Their Usage

### Backend
- **FastAPI**: Modern async Python web framework with automatic API documentation
- **SQLAlchemy**: Database ORM with relationship management
- **Pydantic**: Data validation and serialization

### Frontend
- **@use-gesture/react**: Advanced gesture recognition for swipe detection
- **react-spring**: Physics-based animations for natural motion
- **Framer Motion**: Declarative animations for UI interactions
- **Tailwind CSS**: Utility-first styling with custom watercolor extensions

## Development Notes

### Swipe Gesture Implementation
The swipe system uses velocity-based detection rather than distance-based, making it feel more responsive. Key parameters:
- Velocity threshold: 0.2 for swipe trigger
- Transform calculations: rotation (mx/10), scale (1 - Math.abs(mx)/1000)
- Direction mapping: positive X = like, negative X = dislike

### Animation Performance
- Spring animations use `immediate` flag during active dragging for responsive feedback
- Background cards use CSS transforms (opacity, scale, translateX) for depth effect
- Watercolor animations are CSS-based to avoid JavaScript overhead

### Database Initialization
The application auto-creates tables on startup (`models.Base.metadata.create_all`). For production, use proper migration tools like Alembic.