import React from 'react';
import { motion } from 'framer-motion';
import BookCoverImage from './BookCoverImage';

const BookCard = ({ book, style, isDragging, onLike, onDislike, onSave, nextBookUrls = [] }) => {
  return (
    <motion.div
      className="absolute w-full h-full bg-white/90 rounded-3xl p-8 flex flex-col items-center shadow-xl overflow-hidden"
      style={style}
      animate={isDragging ? { scale: 0.95 } : { scale: 1 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      {/* Watercolor pulse background effect */}
      <div className="absolute inset-0 -top-1/2 -left-1/2 w-[200%] h-[200%] animate-watercolor-pulse">
        <div className="w-full h-full bg-gradient-radial from-watercolor-blue/10 via-watercolor-pink/10 to-watercolor-yellow/10" />
      </div>
      
      {/* Book cover */}
      <div className="relative w-48 h-72 md:w-52 md:h-80 rounded-lg mb-6 overflow-hidden shadow-lg">
        <BookCoverImage 
          src={book.cover_image} 
          alt={book.title}
          className="w-full h-full object-cover"
          preloadUrls={nextBookUrls}
          enableCache={true}
        />
        
        {/* Watercolor border effect */}
        <div className="absolute -inset-1 watercolor-border -z-10 animate-border-flow" />
      </div>
      
      {/* Book info */}
      <div className="text-center flex-grow flex flex-col justify-center z-10">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2 text-shadow">
          {book.title}
        </h2>
        <p className="text-lg md:text-xl text-gray-600 italic mb-4">
          by {book.author}
        </p>
        <p className="text-sm md:text-base leading-relaxed text-gray-800 px-2 mb-6 line-clamp-4">
          {book.description}
        </p>
      </div>
      
      {/* Action buttons */}
      <div className="flex gap-5 z-10">
        <ActionButton 
          onClick={onDislike}
          className="bg-gradient-to-br from-red-400 to-yellow-400"
          icon="✕"
          tooltip="Skip this book"
        />
        <ActionButton 
          onClick={onSave}
          className="bg-gradient-to-br from-yellow-400 to-orange-400"
          icon="📖"
          tooltip="View book details"
        />
        <ActionButton 
          onClick={onLike}
          className="bg-gradient-to-br from-blue-400 to-cyan-400"
          icon="❤️"
          tooltip="Like this book"
        />
      </div>
    </motion.div>
  );
};

const ActionButton = ({ onClick, className, icon, tooltip }) => (
  <motion.button
    onClick={onClick}
    className={`w-14 h-14 rounded-full border-none cursor-pointer shadow-lg relative overflow-hidden ${className}`}
    title={tooltip}
    whileHover={{ 
      scale: 1.1, 
      y: -3,
      boxShadow: "0 8px 25px rgba(0, 0, 0, 0.15)"
    }}
    whileTap={{ scale: 0.95 }}
    transition={{ type: "spring", stiffness: 400, damping: 17 }}
  >
    <div className="absolute inset-0 bg-white/30 rounded-full scale-0 transition-transform duration-500 hover:scale-100" />
    <span className="text-white text-xl relative z-10">{icon}</span>
  </motion.button>
);

export default BookCard;