<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BookFlow - Watercolor Book Discovery</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 25%, #74b9ff 50%, #a29bfe 75%, #fd79a8 100%);
            background-size: 400% 400%;
            animation: watercolorFlow 20s ease infinite;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }

        @keyframes watercolorFlow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Watercolor texture overlay */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 60%);
            pointer-events: none;
            z-index: 1;
        }

        /* Paint splatter effects */
        .paint-splatter {
            position: absolute;
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
            border-radius: 50%;
            filter: blur(20px);
            animation: float 15s ease-in-out infinite;
        }

        .splatter1 {
            top: 10%;
            left: 5%;
            animation-delay: 0s;
        }

        .splatter2 {
            top: 60%;
            right: 10%;
            animation-delay: 5s;
            background: radial-gradient(circle, rgba(116, 185, 255, 0.2) 0%, transparent 70%);
        }

        .splatter3 {
            bottom: 20%;
            left: 15%;
            animation-delay: 10s;
            background: radial-gradient(circle, rgba(253, 121, 168, 0.2) 0%, transparent 70%);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0) scale(1); }
            50% { transform: translateY(-20px) scale(1.1); }
        }

        .container {
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 10;
        }

        .book-carousel {
            position: relative;
            width: 90%;
            max-width: 400px;
            height: 80vh;
            max-height: 600px;
        }

        .book-card {
            position: absolute;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            display: flex;
            flex-direction: column;
            align-items: center;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            opacity: 0;
            transform: translateX(100%) scale(0.8);
            overflow: hidden;
        }

        .book-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: 
                radial-gradient(circle at 30% 30%, rgba(116, 185, 255, 0.1) 0%, transparent 40%),
                radial-gradient(circle at 70% 60%, rgba(253, 121, 168, 0.1) 0%, transparent 40%),
                radial-gradient(circle at 50% 80%, rgba(255, 234, 167, 0.1) 0%, transparent 40%);
            animation: watercolorPulse 8s ease-in-out infinite;
        }

        @keyframes watercolorPulse {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
        }

        .book-card.active {
            opacity: 1;
            transform: translateX(0) scale(1);
            z-index: 5;
        }

        .book-card.prev {
            opacity: 0.5;
            transform: translateX(-100%) scale(0.8);
            z-index: 1;
        }

        .book-card.next {
            opacity: 0.5;
            transform: translateX(100%) scale(0.8);
            z-index: 1;
        }

        .book-cover {
            width: 200px;
            height: 300px;
            border-radius: 10px;
            margin-bottom: 25px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .book-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* Watercolor border effect */
        .book-cover::after {
            content: '';
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            background: linear-gradient(45deg, 
                rgba(116, 185, 255, 0.3) 0%,
                rgba(253, 121, 168, 0.3) 25%,
                rgba(255, 234, 167, 0.3) 50%,
                rgba(162, 155, 254, 0.3) 75%,
                rgba(250, 177, 160, 0.3) 100%);
            filter: blur(10px);
            z-index: -1;
            animation: borderFlow 5s ease-in-out infinite;
        }

        @keyframes borderFlow {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.05); }
        }

        .book-info {
            text-align: center;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            z-index: 1;
        }

        .book-title {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2d3436;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .book-author {
            font-size: 18px;
            color: #636e72;
            margin-bottom: 20px;
            font-style: italic;
        }

        .book-description {
            font-size: 16px;
            line-height: 1.6;
            color: #2d3436;
            padding: 0 10px;
            margin-bottom: 30px;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            z-index: 1;
        }

        .action-btn {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: all 0.5s ease;
        }

        .action-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .action-btn.dislike {
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
        }

        .action-btn.like {
            background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%);
        }

        .action-btn.save {
            background: linear-gradient(135deg, #f9ca24 0%, #f0932b 100%);
        }

        .action-btn:hover {
            transform: translateY(-3px) scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .action-btn svg {
            width: 24px;
            height: 24px;
            fill: white;
            position: relative;
            z-index: 1;
        }

        .navigation {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 20;
        }

        .nav-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-dot.active {
            background: white;
            transform: scale(1.3);
        }

        /* Brush stroke decoration */
        .brush-stroke {
            position: absolute;
            width: 300px;
            height: 100px;
            opacity: 0.1;
            pointer-events: none;
        }

        .brush-stroke svg {
            width: 100%;
            height: 100%;
        }

        .brush1 {
            top: 10%;
            right: -50px;
            transform: rotate(-15deg);
        }

        .brush2 {
            bottom: 15%;
            left: -50px;
            transform: rotate(25deg);
        }

        /* Touch indicators */
        .swipe-hint {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            z-index: 20;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .book-title {
                font-size: 24px;
            }

            .book-author {
                font-size: 16px;
            }

            .book-description {
                font-size: 14px;
            }

            .book-cover {
                width: 180px;
                height: 270px;
            }
        }
    </style>
</head>
<body>
    <!-- Paint splatter effects -->
    <div class="paint-splatter splatter1"></div>
    <div class="paint-splatter splatter2"></div>
    <div class="paint-splatter splatter3"></div>

    <!-- Brush stroke decorations -->
    <div class="brush-stroke brush1">
        <svg viewBox="0 0 300 100">
            <path d="M10,50 Q50,20 100,50 T200,50 T290,50" stroke="rgba(116, 185, 255, 0.3)" stroke-width="20" fill="none" stroke-linecap="round"/>
        </svg>
    </div>
    <div class="brush-stroke brush2">
        <svg viewBox="0 0 300 100">
            <path d="M10,50 Q50,80 100,50 T200,50 T290,50" stroke="rgba(253, 121, 168, 0.3)" stroke-width="20" fill="none" stroke-linecap="round"/>
        </svg>
    </div>

    <div class="container">
        <div class="book-carousel" id="bookCarousel">
            <!-- Book cards will be dynamically generated -->
        </div>
    </div>

    <div class="navigation" id="navigation">
        <!-- Navigation dots will be dynamically generated -->
    </div>

    <div class="swipe-hint">Swipe or use arrow keys</div>

    <script>
        const books = [
            {
                title: "The Ocean at the End of the Lane",
                author: "Neil Gaiman",
                description: "A haunting tale of magic, memory, and the power of stories, painted with the delicate strokes of childhood wonder.",
                cover: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 300'%3E%3Crect fill='%2374b9ff' width='200' height='300'/%3E%3Ctext x='100' y='150' text-anchor='middle' font-size='20' fill='white'%3EOcean%3C/text%3E%3C/svg%3E"
            },
            {
                title: "The Night Circus",
                author: "Erin Morgenstern",
                description: "A mesmerizing story of a magical competition between two young magicians, set against a backdrop of wonder and dreams.",
                cover: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 300'%3E%3Crect fill='%236c5ce7' width='200' height='300'/%3E%3Ctext x='100' y='150' text-anchor='middle' font-size='20' fill='white'%3ECircus%3C/text%3E%3C/svg%3E"
            },
            {
                title: "Big Magic",
                author: "Elizabeth Gilbert",
                description: "Creative living beyond fear - a watercolor journey through the artistic process and the courage to create.",
                cover: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 300'%3E%3Crect fill='%23fd79a8' width='200' height='300'/%3E%3Ctext x='100' y='150' text-anchor='middle' font-size='20' fill='white'%3EMagic%3C/text%3E%3C/svg%3E"
            },
            {
                title: "The Starless Sea",
                author: "Erin Morgenstern",
                description: "A sweeping tale of pirates, painters, lovers and liars, woven together through stories within stories.",
                cover: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 300'%3E%3Crect fill='%23a29bfe' width='200' height='300'/%3E%3Ctext x='100' y='150' text-anchor='middle' font-size='20' fill='white'%3ESea%3C/text%3E%3C/svg%3E"
            },
            {
                title: "The Invisible Life of Addie LaRue",
                author: "V.E. Schwab",
                description: "A young woman makes a Faustian bargain to live forever but is cursed to be forgotten by everyone she meets.",
                cover: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 200 300'%3E%3Crect fill='%23fab1a0' width='200' height='300'/%3E%3Ctext x='100' y='150' text-anchor='middle' font-size='20' fill='white'%3EAddie%3C/text%3E%3C/svg%3E"
            }
        ];

        let currentIndex = 0;
        let startX = 0;
        let currentX = 0;
        let cardBeingDragged = null;

        function createBookCard(book, index) {
            const card = document.createElement('div');
            card.className = 'book-card';
            card.innerHTML = `
                <div class="book-cover">
                    <img src="${book.cover}" alt="${book.title}">
                </div>
                <div class="book-info">
                    <h2 class="book-title">${book.title}</h2>
                    <p class="book-author">by ${book.author}</p>
                    <p class="book-description">${book.description}</p>
                </div>
                <div class="action-buttons">
                    <button class="action-btn dislike" onclick="handleDislike()">
                        <svg viewBox="0 0 24 24">
                            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                        </svg>
                    </button>
                    <button class="action-btn save" onclick="handleSave()">
                        <svg viewBox="0 0 24 24">
                            <path d="M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2z"/>
                        </svg>
                    </button>
                    <button class="action-btn like" onclick="handleLike()">
                        <svg viewBox="0 0 24 24">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                    </button>
                </div>
            `;
            return card;
        }

        function createNavigationDot(index) {
            const dot = document.createElement('div');
            dot.className = 'nav-dot';
            dot.onclick = () => goToBook(index);
            return dot;
        }

        function initializeCarousel() {
            const carousel = document.getElementById('bookCarousel');
            const navigation = document.getElementById('navigation');

            books.forEach((book, index) => {
                const card = createBookCard(book, index);
                carousel.appendChild(card);

                const dot = createNavigationDot(index);
                navigation.appendChild(dot);
            });

            updateCarousel();
        }

        function updateCarousel() {
            const cards = document.querySelectorAll('.book-card');
            const dots = document.querySelectorAll('.nav-dot');

            cards.forEach((card, index) => {
                card.classList.remove('active', 'prev', 'next');
                
                if (index === currentIndex) {
                    card.classList.add('active');
                } else if (index === (currentIndex - 1 + books.length) % books.length) {
                    card.classList.add('prev');
                } else if (index === (currentIndex + 1) % books.length) {
                    card.classList.add('next');
                }
            });

            dots.forEach((dot, index) => {
                dot.classList.toggle('active', index === currentIndex);
            });
        }

        function nextBook() {
            currentIndex = (currentIndex + 1) % books.length;
            updateCarousel();
        }

        function prevBook() {
            currentIndex = (currentIndex - 1 + books.length) % books.length;
            updateCarousel();
        }

        function goToBook(index) {
            currentIndex = index;
            updateCarousel();
        }

        function handleLike() {
            console.log('Liked:', books[currentIndex].title);
            setTimeout(nextBook, 300);
        }

        function handleDislike() {
            console.log('Disliked:', books[currentIndex].title);
            setTimeout(nextBook, 300);
        }

        function handleSave() {
            console.log('Saved:', books[currentIndex].title);
            const saveBtn = event.currentTarget;
            saveBtn.style.transform = 'scale(1.2)';
            setTimeout(() => {
                saveBtn.style.transform = '';
            }, 300);
        }

        // Touch events
        const carousel = document.getElementById('bookCarousel');

        carousel.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            cardBeingDragged = document.querySelector('.book-card.active');
        });

        carousel.addEventListener('touchmove', (e) => {
            if (!cardBeingDragged) return;
            
            currentX = e.touches[0].clientX;
            const diffX = currentX - startX;
            
            cardBeingDragged.style.transform = `translateX(${diffX}px) scale(${1 - Math.abs(diffX) / 500})`;
            cardBeingDragged.style.opacity = 1 - Math.abs(diffX) / 300;
        });

        carousel.addEventListener('touchend', () => {
            if (!cardBeingDragged) return;
            
            const diffX = currentX - startX;
            const threshold = 100;
            
            if (Math.abs(diffX) > threshold) {
                if (diffX > 0) {
                    prevBook();
                } else {
                    nextBook();
                }
            } else {
                cardBeingDragged.style.transform = '';
                cardBeingDragged.style.opacity = '';
            }
            
            cardBeingDragged = null;
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                prevBook();
            } else if (e.key === 'ArrowRight') {
                nextBook();
            }
        });

        // Mouse swipe support
        let mouseDown = false;

        carousel.addEventListener('mousedown', (e) => {
            mouseDown = true;
            startX = e.clientX;
            cardBeingDragged = document.querySelector('.book-card.active');
            e.preventDefault();
        });

        carousel.addEventListener('mousemove', (e) => {
            if (!mouseDown || !cardBeingDragged) return;
            
            currentX = e.clientX;
            const diffX = currentX - startX;
            
            cardBeingDragged.style.transform = `translateX(${diffX}px) scale(${1 - Math.abs(diffX) / 500})`;
            cardBeingDragged.style.opacity = 1 - Math.abs(diffX) / 300;
        });

        carousel.addEventListener('mouseup', () => {
            if (!cardBeingDragged) return;
            
            const diffX = currentX - startX;
            const threshold = 100;
            
            if (Math.abs(diffX) > threshold) {
                if (diffX > 0) {
                    prevBook();
                } else {
                    nextBook();
                }
            } else {
                cardBeingDragged.style.transform = '';
                cardBeingDragged.style.opacity = '';
            }
            
            mouseDown = false;
            cardBeingDragged = null;
        });

        carousel.addEventListener('mouseleave', () => {
            if (cardBeingDragged) {
                cardBeingDragged.style.transform = '';
                cardBeingDragged.style.opacity = '';
            }
            mouseDown = false;
            cardBeingDragged = null;
        });

        // Initialize the carousel
        initializeCarousel();
    </script>
</body>
</html>