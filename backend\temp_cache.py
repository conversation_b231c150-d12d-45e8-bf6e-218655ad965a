import asyncio
import time
from typing import Dict, <PERSON>tional, <PERSON><PERSON>
import aiohttp
from io import BytesIO
from PIL import Image
import logging

logger = logging.getLogger(__name__)

class TemporaryImageCache:
    """
    Minimal temporary cache for the first few book images.
    This helps with initial page load performance before client-side caching takes over.
    """
    
    def __init__(self, max_items: int = 5, ttl_seconds: int = 86400):
        """
        Initialize the temporary cache.
        
        Args:
            max_items: Maximum number of images to cache (default: 5)
            ttl_seconds: Time to live in seconds (default: 24 hours)
        """
        self.max_items = max_items
        self.ttl_seconds = ttl_seconds
        self.cache: Dict[str, Tuple[bytes, float, str]] = {}  # url -> (data, timestamp, content_type)
        self.access_order = []  # Track access order for LRU eviction
        self._lock = asyncio.Lock()
        self._cleanup_task = None
        
    async def start(self):
        """Start the periodic cleanup task."""
        if self._cleanup_task is None:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            
    async def stop(self):
        """Stop the periodic cleanup task."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            self._cleanup_task = None
    
    async def get_or_fetch(self, url: str) -> Optional[Tuple[bytes, str]]:
        """
        Get image from cache or fetch and cache it.
        
        Args:
            url: The image URL to fetch
            
        Returns:
            Tuple of (image_data, content_type) or None if failed
        """
        async with self._lock:
            # Check if in cache and not expired
            if url in self.cache:
                data, timestamp, content_type = self.cache[url]
                if time.time() - timestamp < self.ttl_seconds:
                    # Update access order
                    if url in self.access_order:
                        self.access_order.remove(url)
                    self.access_order.append(url)
                    logger.debug(f"Cache hit for {url}")
                    return data, content_type
                else:
                    # Expired, remove it
                    del self.cache[url]
                    if url in self.access_order:
                        self.access_order.remove(url)
                    logger.debug(f"Cache expired for {url}")
        
        # Not in cache or expired, fetch it
        try:
            image_data, content_type = await self._fetch_and_optimize_image(url)
            if image_data:
                await self._add_to_cache(url, image_data, content_type)
                return image_data, content_type
        except Exception as e:
            logger.warning(f"Failed to fetch image {url}: {e}")
            
        return None
    
    async def _fetch_and_optimize_image(self, url: str) -> Tuple[Optional[bytes], Optional[str]]:
        """Fetch and optimize image from URL."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status != 200:
                        return None, None
                    
                    content_type = response.headers.get('Content-Type', 'image/jpeg')
                    image_data = await response.read()
                    
                    # Optimize image if it's too large
                    if len(image_data) > 500 * 1024:  # 500KB
                        image_data = self._optimize_image(image_data)
                        content_type = 'image/jpeg'
                    
                    return image_data, content_type
                    
        except Exception as e:
            logger.error(f"Error fetching image from {url}: {e}")
            return None, None
    
    def _optimize_image(self, image_data: bytes) -> bytes:
        """Optimize image size and quality."""
        try:
            img = Image.open(BytesIO(image_data))
            
            # Convert RGBA to RGB with white background
            if img.mode in ('RGBA', 'LA', 'P'):
                background = Image.new('RGB', img.size, (255, 255, 255))
                if img.mode == 'P':
                    img = img.convert('RGBA')
                background.paste(img, mask=img.split()[-1])
                img = background
            
            # Resize if too large
            max_width = 400
            if img.width > max_width:
                ratio = max_width / img.width
                new_height = int(img.height * ratio)
                img = img.resize((max_width, new_height), Image.Resampling.LANCZOS)
            
            # Save optimized image
            output = BytesIO()
            img.save(output, format='JPEG', quality=85, optimize=True)
            return output.getvalue()
            
        except Exception as e:
            logger.warning(f"Failed to optimize image: {e}")
            return image_data
    
    async def _add_to_cache(self, url: str, data: bytes, content_type: str):
        """Add image to cache with LRU eviction."""
        async with self._lock:
            # Remove oldest if at capacity
            if len(self.cache) >= self.max_items and url not in self.cache:
                if self.access_order:
                    oldest_url = self.access_order.pop(0)
                    del self.cache[oldest_url]
                    logger.debug(f"Evicted {oldest_url} from cache")
            
            # Add to cache
            self.cache[url] = (data, time.time(), content_type)
            if url in self.access_order:
                self.access_order.remove(url)
            self.access_order.append(url)
            logger.info(f"Added {url} to cache (size: {len(data)} bytes)")
    
    async def remove(self, url: str):
        """Remove an image from cache (e.g., when client confirms it's cached)."""
        async with self._lock:
            if url in self.cache:
                del self.cache[url]
                if url in self.access_order:
                    self.access_order.remove(url)
                logger.info(f"Removed {url} from cache (client cached)")
    
    async def clear(self):
        """Clear all cached images."""
        async with self._lock:
            self.cache.clear()
            self.access_order.clear()
            logger.info("Cleared temporary image cache")
    
    async def get_stats(self) -> dict:
        """Get cache statistics."""
        async with self._lock:
            total_size = sum(len(data) for data, _, _ in self.cache.values())
            return {
                "count": len(self.cache),
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "max_items": self.max_items,
                "ttl_seconds": self.ttl_seconds,
                "cached_urls": list(self.cache.keys())
            }
    
    async def _periodic_cleanup(self):
        """Periodically clean up expired entries."""
        while True:
            try:
                await asyncio.sleep(3600)  # Check every hour
                
                async with self._lock:
                    current_time = time.time()
                    expired_urls = [
                        url for url, (_, timestamp, _) in self.cache.items()
                        if current_time - timestamp >= self.ttl_seconds
                    ]
                    
                    for url in expired_urls:
                        del self.cache[url]
                        if url in self.access_order:
                            self.access_order.remove(url)
                    
                    if expired_urls:
                        logger.info(f"Cleaned up {len(expired_urls)} expired cache entries")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in periodic cleanup: {e}")

# Global instance
temp_image_cache = TemporaryImageCache()