import React, { useState } from 'react';
import BookCarousel from './components/BookCarousel';
import WatercolorBackground from './components/WatercolorBackground';
import ProfileChat from './components/ProfileChat';
import AddBookForm from './components/AddBookForm';
import UserLibrary from './components/UserLibrary';

function App() {
  const [showProfileChat, setShowProfileChat] = useState(false);
  const [showAddBook, setShowAddBook] = useState(false);
  const [showLibrary, setShowLibrary] = useState(false);

  return (
    <div className="App">
      <WatercolorBackground />
      <BookCarousel />
      
      {/* Navigation Menu */}
      <div className="fixed top-4 right-4 z-30 space-y-2">
        <button
          onClick={() => setShowProfileChat(true)}
          className="bg-white/20 backdrop-blur-sm text-white p-3 rounded-full hover:bg-white/30 transition-colors shadow-lg"
          title="Update Reading Preferences"
        >
          ⚙️
        </button>
        <button
          onClick={() => setShowAddBook(true)}
          className="bg-white/20 backdrop-blur-sm text-white p-3 rounded-full hover:bg-white/30 transition-colors shadow-lg"
          title="Add Book to Library"
        >
          📚
        </button>
        <button
          onClick={() => setShowLibrary(true)}
          className="bg-white/20 backdrop-blur-sm text-white p-3 rounded-full hover:bg-white/30 transition-colors shadow-lg"
          title="View My Library"
        >
          📖
        </button>
      </div>

      {/* Modals */}
      <ProfileChat
        isOpen={showProfileChat}
        onClose={() => setShowProfileChat(false)}
      />
      <AddBookForm
        isOpen={showAddBook}
        onClose={() => setShowAddBook(false)}
        onBookAdded={() => {
          // Could refresh recommendations here
          console.log('Book added to library');
        }}
      />
      <UserLibrary
        isOpen={showLibrary}
        onClose={() => setShowLibrary(false)}
      />
    </div>
  );
}

export default App;